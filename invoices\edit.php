<?php
/**
 * Apex Company Management System
 * Edit Invoice
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('edit_invoices');

$invoice_id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

if (!$invoice_id) {
    $_SESSION['error'] = 'Invalid invoice ID.';
    header('Location: index.php');
    exit();
}

// Get invoice details
$stmt = $mysqli->prepare("SELECT * FROM invoices WHERE id = ?");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$invoice = $stmt->get_result()->fetch_assoc();

if (!$invoice) {
    $_SESSION['error'] = 'Invoice not found.';
    header('Location: index.php');
    exit();
}

// Get invoice items
$stmt = $mysqli->prepare("SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY id");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$existing_items = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Handle form submission
if ($_POST && verify_csrf_token($_POST['csrf_token'])) {
    $client_id = (int)$_POST['client_id'];
    $project_id = !empty($_POST['project_id']) ? (int)$_POST['project_id'] : null;
    $issue_date = sanitize_input($_POST['issue_date']);
    $due_date = !empty($_POST['due_date']) ? sanitize_input($_POST['due_date']) : null;
    $tax_rate = (float)$_POST['tax_rate'];
    $discount_amount = (float)$_POST['discount_amount'];
    $notes = sanitize_input($_POST['notes']);
    $payment_terms = sanitize_input($_POST['terms']);
    $status = sanitize_input($_POST['status']);
    
    // Validate required fields
    if (!$client_id) {
        $error = 'Please select a client.';
    } elseif (empty($issue_date)) {
        $error = 'Issue date is required.';
    } else {
        // Process invoice items
        $items = $_POST['items'] ?? [];
        $valid_items = [];
        $subtotal = 0;
        
        foreach ($items as $item) {
            $description = trim($item['description']);
            $quantity = (float)$item['quantity'];
            $rate = (float)$item['rate'];
            
            if (!empty($description) && $quantity > 0 && $rate > 0) {
                $valid_items[] = [
                    'description' => $description,
                    'quantity' => $quantity,
                    'rate' => $rate
                ];
                $subtotal += $quantity * $rate;
            }
        }
        
        if (empty($valid_items)) {
            $error = 'Please add at least one valid invoice item.';
        } else {
            // Calculate totals
            $tax_amount = ($subtotal * $tax_rate) / 100;
            $total_amount = $subtotal + $tax_amount - $discount_amount;
            
            // Update invoice
            $stmt = $mysqli->prepare("
                UPDATE invoices SET
                    client_id = ?, project_id = ?, issue_date = ?, due_date = ?,
                    tax_rate = ?, discount_amount = ?, total_amount = ?,
                    notes = ?, terms = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->bind_param("iissddsssi", 
                $client_id, $project_id, $issue_date, $due_date,
                $tax_rate, $discount_amount, $total_amount,
                $notes, $payment_terms, $status, $invoice_id
            );
            
            if ($stmt->execute()) {
                // Delete existing items
                $stmt = $mysqli->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
                $stmt->bind_param("i", $invoice_id);
                $stmt->execute();
                
                // Insert new items
                $stmt = $mysqli->prepare("
                    INSERT INTO invoice_items (invoice_id, description, quantity, rate)
                    VALUES (?, ?, ?, ?)
                ");
                
                foreach ($valid_items as $item) {
                    $stmt->bind_param("isdd", 
                        $invoice_id, $item['description'], 
                        $item['quantity'], $item['rate']
                    );
                    $stmt->execute();
                }
                
                log_activity('Invoice Updated', 'invoices', $invoice_id);
                $_SESSION['success'] = 'Invoice updated successfully!';
                header('Location: view.php?id=' . $invoice_id);
                exit();
            } else {
                $error = 'Error updating invoice. Please try again.';
            }
        }
    }
}

// Get clients for dropdown
$clients = $mysqli->query("SELECT id, company_name FROM clients ORDER BY company_name");

// Get projects for dropdown
$projects = $mysqli->query("SELECT id, project_name FROM projects ORDER BY project_name");

$page_title = 'Edit Invoice #' . $invoice['invoice_number'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-pencil-square"></i>
                    Edit Invoice #<?= htmlspecialchars($invoice['invoice_number']) ?>
                </h1>
                <div class="btn-group">
                    <a href="view.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Invoice
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-list"></i>
                        All Invoices
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <form method="POST" action="" id="invoiceForm">
        <?= csrf_token_input() ?>
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Invoice Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Client *</label>
                                    <select class="form-select" id="client_id" name="client_id" required>
                                        <option value="">Select Client</option>
                                        <?php while ($client = $clients->fetch_assoc()): ?>
                                        <option value="<?= $client['id'] ?>" <?= $client['id'] == $invoice['client_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($client['company_name']) ?>
                                        </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Project (Optional)</label>
                                    <select class="form-select" id="project_id" name="project_id">
                                        <option value="">Select Project</option>
                                        <?php while ($project = $projects->fetch_assoc()): ?>
                                        <option value="<?= $project['id'] ?>" <?= $project['id'] == $invoice['project_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($project['project_name']) ?>
                                        </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="issue_date" class="form-label">Issue Date *</label>
                                    <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                           value="<?= $invoice['issue_date'] ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="due_date" class="form-label">Due Date</label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" 
                                           value="<?= $invoice['due_date'] ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="draft" <?= $invoice['status'] == 'draft' ? 'selected' : '' ?>>Draft</option>
                                        <option value="sent" <?= $invoice['status'] == 'sent' ? 'selected' : '' ?>>Sent</option>
                                        <option value="paid" <?= $invoice['status'] == 'paid' ? 'selected' : '' ?>>Paid</option>
                                        <option value="overdue" <?= $invoice['status'] == 'overdue' ? 'selected' : '' ?>>Overdue</option>
                                        <option value="cancelled" <?= $invoice['status'] == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Items -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label">Invoice Items *</label>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addInvoiceItem()">
                                    <i class="bi bi-plus"></i> Add Item
                                </button>
                            </div>
                            <div id="invoiceItems">
                                <!-- Items will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"><?= htmlspecialchars($invoice['notes']) ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="terms" class="form-label">Payment Terms</label>
                                    <textarea class="form-control" id="terms" name="terms" rows="3"><?= htmlspecialchars($invoice['terms']) ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Invoice Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                       value="<?= $invoice['tax_rate'] ?>" min="0" max="100" step="0.01" onchange="calculateTotals()">
                            </div>
                            <div class="col-6">
                                <label for="discount_amount" class="form-label">Discount (₹)</label>
                                <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                       value="<?= $invoice['discount_amount'] ?>" min="0" step="0.01" onchange="calculateTotals()">
                            </div>
                        </div>

                        <div class="border-top pt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span id="subtotalDisplay">₹0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax:</span>
                                <span id="taxDisplay">₹0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Discount:</span>
                                <span id="discountDisplay">-₹0.00</span>
                            </div>
                            <div class="d-flex justify-content-between border-top pt-2">
                                <strong>Total:</strong>
                                <strong id="totalDisplay">₹0.00</strong>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-check"></i>
                                Update Invoice
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
let itemCounter = 0;
const existingItems = <?= json_encode($existing_items) ?>;

function addInvoiceItem(description = '', quantity = 1, rate = 0) {
    itemCounter++;
    const itemsContainer = document.getElementById('invoiceItems');
    
    const itemDiv = document.createElement('div');
    itemDiv.className = 'card mb-2';
    itemDiv.id = 'item_' + itemCounter;
    
    itemDiv.innerHTML = `
        <div class="card-body">
            <div class="row">
                <div class="col-md-5">
                    <input type="text" class="form-control" name="items[${itemCounter}][description]" 
                           placeholder="Item description" value="${description}" required>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" name="items[${itemCounter}][quantity]" 
                           placeholder="Qty" value="${quantity}" min="0.01" step="0.01" onchange="calculateTotals()" required>
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="items[${itemCounter}][rate]" 
                           placeholder="Rate" value="${rate}" min="0.01" step="0.01" onchange="calculateTotals()" required>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeInvoiceItem(${itemCounter})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    itemsContainer.appendChild(itemDiv);
    calculateTotals();
}

function removeInvoiceItem(itemId) {
    const item = document.getElementById('item_' + itemId);
    if (item) {
        item.remove();
        calculateTotals();
    }
}

function calculateTotals() {
    let subtotal = 0;
    
    // Calculate subtotal from all items
    const quantityInputs = document.querySelectorAll('input[name*="[quantity]"]');
    const rateInputs = document.querySelectorAll('input[name*="[rate]"]');
    
    for (let i = 0; i < quantityInputs.length; i++) {
        const quantity = parseFloat(quantityInputs[i].value) || 0;
        const rate = parseFloat(rateInputs[i].value) || 0;
        subtotal += quantity * rate;
    }
    
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    
    const taxAmount = (subtotal * taxRate) / 100;
    const total = subtotal + taxAmount - discountAmount;
    
    document.getElementById('subtotalDisplay').textContent = '₹' + subtotal.toFixed(2);
    document.getElementById('taxDisplay').textContent = '₹' + taxAmount.toFixed(2);
    document.getElementById('discountDisplay').textContent = '-₹' + discountAmount.toFixed(2);
    document.getElementById('totalDisplay').textContent = '₹' + total.toFixed(2);
}

// Load existing items
document.addEventListener('DOMContentLoaded', function() {
    if (existingItems.length > 0) {
        existingItems.forEach(item => {
            addInvoiceItem(item.description, item.quantity, item.rate);
        });
    } else {
        addInvoiceItem();
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
