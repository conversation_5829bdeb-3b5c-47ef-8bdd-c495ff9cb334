<?php
/**
 * Apex Company Management System
 * Invoice PDF Generation
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_invoices');

$invoice_id = (int)($_GET['id'] ?? 0);

if (!$invoice_id) {
    $_SESSION['error'] = 'Invalid invoice ID.';
    header('Location: index.php');
    exit();
}

// Get invoice details with client and project info
$stmt = $mysqli->prepare("
    SELECT i.*,
           c.company_name as client_name,
           c.email as client_email,
           c.phone as client_phone,
           c.address as client_address,
           p.project_name
    FROM invoices i
    LEFT JOIN clients c ON i.client_id = c.id
    LEFT JOIN projects p ON i.project_id = p.id
    WHERE i.id = ?
");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$invoice = $stmt->get_result()->fetch_assoc();

if (!$invoice) {
    $_SESSION['error'] = 'Invoice not found.';
    header('Location: index.php');
    exit();
}

// Get invoice items
$stmt = $mysqli->prepare("
    SELECT * FROM invoice_items 
    WHERE invoice_id = ? 
    ORDER BY id
");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$items = $stmt->get_result();

// Set headers for PDF download
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="Invoice-' . $invoice['invoice_number'] . '.pdf"');

// For now, we'll generate a simple HTML version that can be printed as PDF
// In a production environment, you would use a library like TCPDF or DOMPDF
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice <?= htmlspecialchars($invoice['invoice_number']) ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-info {
            text-align: right;
            margin-bottom: 20px;
        }
        .invoice-info {
            margin-bottom: 30px;
        }
        .client-info {
            margin-bottom: 30px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }
        .totals-table td {
            padding: 5px 10px;
            border-bottom: 1px solid #ddd;
        }
        .total-row {
            font-weight: bold;
            border-top: 2px solid #333;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <h2>Your Company Name</h2>
            <p>Your Address<br>
            City, State ZIP<br>
            Phone: Your Phone<br>
            Email: <EMAIL></p>
        </div>
        
        <h1 style="color: #007bff; margin: 0;">INVOICE</h1>
    </div>

    <div class="invoice-info">
        <table style="width: 100%;">
            <tr>
                <td style="width: 50%;">
                    <strong>Invoice Number:</strong> <?= htmlspecialchars($invoice['invoice_number']) ?><br>
                    <strong>Issue Date:</strong> <?= format_date($invoice['issue_date']) ?><br>
                    <?php if ($invoice['due_date']): ?>
                    <strong>Due Date:</strong> <?= format_date($invoice['due_date']) ?><br>
                    <?php endif; ?>
                    <strong>Status:</strong> <?= ucfirst($invoice['status']) ?>
                </td>
                <td style="width: 50%; text-align: right;">
                    <?php if ($invoice['project_name']): ?>
                    <strong>Project:</strong> <?= htmlspecialchars($invoice['project_name']) ?>
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>

    <div class="client-info">
        <h3>Bill To:</h3>
        <?php if ($invoice['client_name']): ?>
        <strong><?= htmlspecialchars($invoice['client_name']) ?></strong><br>
        <?php if ($invoice['client_address']): ?>
        <?= nl2br(htmlspecialchars($invoice['client_address'])) ?><br>
        <?php endif; ?>
        <?php if ($invoice['client_phone']): ?>
        Phone: <?= htmlspecialchars($invoice['client_phone']) ?><br>
        <?php endif; ?>
        <?php if ($invoice['client_email']): ?>
        Email: <?= htmlspecialchars($invoice['client_email']) ?>
        <?php endif; ?>
        <?php else: ?>
        <em>No client assigned</em>
        <?php endif; ?>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="text-center" style="width: 80px;">Quantity</th>
                <th class="text-right" style="width: 100px;">Rate</th>
                <th class="text-right" style="width: 100px;">Amount</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $subtotal = 0;
            while ($item = $items->fetch_assoc()): 
                $amount = $item['quantity'] * $item['rate'];
                $subtotal += $amount;
            ?>
            <tr>
                <td><?= htmlspecialchars($item['description']) ?></td>
                <td class="text-center"><?= $item['quantity'] ?></td>
                <td class="text-right"><?= format_currency($item['rate']) ?></td>
                <td class="text-right"><?= format_currency($amount) ?></td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>

    <table class="totals-table">
        <tr>
            <td><strong>Subtotal:</strong></td>
            <td class="text-right"><?= format_currency($subtotal) ?></td>
        </tr>
        <?php if ($invoice['tax_rate'] > 0): ?>
        <tr>
            <td>Tax (<?= $invoice['tax_rate'] ?>%):</td>
            <td class="text-right"><?= format_currency(($subtotal * $invoice['tax_rate']) / 100) ?></td>
        </tr>
        <?php endif; ?>
        <?php if ($invoice['discount_amount'] > 0): ?>
        <tr>
            <td>Discount:</td>
            <td class="text-right">-<?= format_currency($invoice['discount_amount']) ?></td>
        </tr>
        <?php endif; ?>
        <tr class="total-row">
            <td><strong>Total:</strong></td>
            <td class="text-right"><strong><?= format_currency($invoice['total_amount']) ?></strong></td>
        </tr>
    </table>

    <?php if ($invoice['notes']): ?>
    <div class="notes">
        <h4>Notes:</h4>
        <p><?= nl2br(htmlspecialchars($invoice['notes'])) ?></p>
    </div>
    <?php endif; ?>

    <?php if ($invoice['terms']): ?>
    <div class="notes">
        <h4>Payment Terms:</h4>
        <p><?= nl2br(htmlspecialchars($invoice['terms'])) ?></p>
    </div>
    <?php endif; ?>

    <script>
        // Auto-print when page loads (for PDF generation)
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
