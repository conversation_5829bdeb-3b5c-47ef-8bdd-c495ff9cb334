<?php
/**
 * Apex Company Management System
 * Invoice Reports
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_reports');

// Get date range parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');

// Get invoice statistics
$invoice_stats_query = "
    SELECT 
        COUNT(*) as total_invoices,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as pending_invoices,
        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invoices,
        SUM(total_amount) as total_amount,
        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN status = 'sent' THEN total_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'overdue' THEN total_amount ELSE 0 END) as overdue_amount,
        AVG(total_amount) as average_invoice_value
    FROM invoices 
    WHERE issue_date BETWEEN ? AND ?
";
$stmt = $mysqli->prepare($invoice_stats_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$invoice_stats = $stmt->get_result()->fetch_assoc();

// Get payment trends
$payment_trends_query = "
    SELECT 
        DATE_FORMAT(issue_date, '%Y-%m') as month,
        COUNT(*) as invoices_issued,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as invoices_paid,
        SUM(total_amount) as amount_issued,
        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as amount_paid
    FROM invoices 
    WHERE issue_date >= DATE_SUB(?, INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(issue_date, '%Y-%m')
    ORDER BY month DESC
    LIMIT 6
";
$stmt = $mysqli->prepare($payment_trends_query);
$stmt->bind_param("s", $end_date);
$stmt->execute();
$payment_trends = $stmt->get_result();

// Get overdue invoices
$overdue_query = "
    SELECT 
        i.id,
        i.invoice_number,
        i.total_amount,
        i.due_date,
        i.issue_date,
        c.company_name as client_name,
        DATEDIFF(CURDATE(), i.due_date) as days_overdue
    FROM invoices i
    LEFT JOIN clients c ON i.client_id = c.id
    WHERE i.status = 'overdue' 
    AND i.due_date < CURDATE()
    ORDER BY days_overdue DESC
    LIMIT 20
";
$overdue_invoices = $mysqli->query($overdue_query);

$page_title = 'Invoice Reports';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-receipt"></i>
                    Invoice Reports
                </h1>
                <div class="btn-group">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Reports
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="bi bi-search"></i>
                                Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $invoice_stats['total_invoices'] ?></h4>
                            <p class="mb-0">Total Invoices</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-receipt" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75">Avg: <?= format_currency($invoice_stats['average_invoice_value']) ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($invoice_stats['paid_amount']) ?></h4>
                            <p class="mb-0">Paid Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75"><?= $invoice_stats['paid_invoices'] ?> invoices</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($invoice_stats['pending_amount']) ?></h4>
                            <p class="mb-0">Pending Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75"><?= $invoice_stats['pending_invoices'] ?> invoices</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($invoice_stats['overdue_amount']) ?></h4>
                            <p class="mb-0">Overdue Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75"><?= $invoice_stats['overdue_invoices'] ?> invoices</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Payment Trends -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Payment Trends (Last 6 Months)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Invoices Issued</th>
                                    <th>Invoices Paid</th>
                                    <th>Amount Issued</th>
                                    <th>Amount Paid</th>
                                    <th>Collection Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($trend = $payment_trends->fetch_assoc()): ?>
                                <?php 
                                $collection_rate = $trend['amount_issued'] > 0 ? 
                                    ($trend['amount_paid'] / $trend['amount_issued'] * 100) : 0;
                                ?>
                                <tr>
                                    <td><?= date('M Y', strtotime($trend['month'] . '-01')) ?></td>
                                    <td><?= $trend['invoices_issued'] ?></td>
                                    <td><?= $trend['invoices_paid'] ?></td>
                                    <td><?= format_currency($trend['amount_issued']) ?></td>
                                    <td><?= format_currency($trend['amount_paid']) ?></td>
                                    <td>
                                        <span class="badge <?= $collection_rate >= 80 ? 'bg-success' : ($collection_rate >= 60 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= number_format($collection_rate, 1) ?>%
                                        </span>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overdue Invoices -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Overdue Invoices</h5>
                </div>
                <div class="card-body">
                    <?php if ($overdue_invoices->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($overdue = $overdue_invoices->fetch_assoc()): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <a href="../invoices/view.php?id=<?= $overdue['id'] ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($overdue['invoice_number']) ?>
                                    </a>
                                </h6>
                                <small class="text-danger"><?= $overdue['days_overdue'] ?> days</small>
                            </div>
                            <p class="mb-1">
                                <strong><?= format_currency($overdue['total_amount']) ?></strong>
                                <?php if ($overdue['client_name']): ?>
                                <br><small class="text-muted"><?= htmlspecialchars($overdue['client_name']) ?></small>
                                <?php endif; ?>
                            </p>
                            <small class="text-muted">Due: <?= format_date($overdue['due_date']) ?></small>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        <p class="mt-2 mb-0">No overdue invoices</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Status Distribution -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Invoice Status Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h3 class="text-primary"><?= $invoice_stats['total_invoices'] ?></h3>
                                <p class="mb-0">Total</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h3 class="text-success"><?= $invoice_stats['paid_invoices'] ?></h3>
                                <p class="mb-0">Paid</p>
                                <small class="text-muted">
                                    <?= $invoice_stats['total_invoices'] > 0 ? 
                                        number_format(($invoice_stats['paid_invoices'] / $invoice_stats['total_invoices']) * 100, 1) : 0 ?>%
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h3 class="text-warning"><?= $invoice_stats['pending_invoices'] ?></h3>
                                <p class="mb-0">Pending</p>
                                <small class="text-muted">
                                    <?= $invoice_stats['total_invoices'] > 0 ? 
                                        number_format(($invoice_stats['pending_invoices'] / $invoice_stats['total_invoices']) * 100, 1) : 0 ?>%
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h3 class="text-danger"><?= $invoice_stats['overdue_invoices'] ?></h3>
                                <p class="mb-0">Overdue</p>
                                <small class="text-muted">
                                    <?= $invoice_stats['total_invoices'] > 0 ? 
                                        number_format(($invoice_stats['overdue_invoices'] / $invoice_stats['total_invoices']) * 100, 1) : 0 ?>%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
