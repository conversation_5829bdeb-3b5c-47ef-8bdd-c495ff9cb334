<?php
/**
 * Apex Company Management System
 * Common Functions
 */

if (!defined('APEX_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Sanitize input data
 */
function sanitize_input($data) {
    if (is_array($data)) {
        return array_map('sanitize_input', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Generate secure random token
 */
function generate_token($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Hash password
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Check if user is logged in
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

/**
 * Check if user has permission
 */
function has_permission($permission, $user_id = null) {
    global $mysqli;
    
    if (!is_logged_in()) {
        return false;
    }
    
    $user_id = $user_id ?? $_SESSION['user_id'];
    $user_role = $_SESSION['user_role'];
    
    // Super admin has all permissions
    if ($user_role === 'super_admin') {
        return true;
    }
    
    // Check role permissions
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as count FROM role_permissions rp 
        JOIN permissions p ON rp.permission_id = p.id 
        WHERE rp.role = ? AND p.name = ?
    ");
    $stmt->bind_param("ss", $user_role, $permission);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    if ($result['count'] > 0) {
        // Check for user-specific permission overrides
        $stmt = $mysqli->prepare("
            SELECT granted FROM user_permissions up 
            JOIN permissions p ON up.permission_id = p.id 
            WHERE up.user_id = ? AND p.name = ?
        ");
        $stmt->bind_param("is", $user_id, $permission);
        $stmt->execute();
        $override = $stmt->get_result()->fetch_assoc();
        
        if ($override) {
            return (bool)$override['granted'];
        }
        
        return true;
    }
    
    return false;
}

/**
 * Require permission or redirect
 */
function require_permission($permission) {
    if (!has_permission($permission)) {
        $_SESSION['error'] = 'Access denied. You do not have permission to access this resource.';
        header('Location: ' . APP_URL . 'dashboard/');
        exit();
    }
}

/**
 * Check login attempts for user
 */
function check_login_attempts($username) {
    global $mysqli;

    $stmt = $mysqli->prepare("SELECT login_attempts, locked_until FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $username, $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        // Check if account is locked
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            return ['locked' => true, 'attempts' => $user['login_attempts']];
        }

        // Check if too many attempts
        if ($user['login_attempts'] >= 5) {
            // Lock account for 15 minutes
            $lock_until = date('Y-m-d H:i:s', time() + (15 * 60));
            $stmt = $mysqli->prepare("UPDATE users SET locked_until = ? WHERE username = ? OR email = ?");
            $stmt->bind_param("sss", $lock_until, $username, $username);
            $stmt->execute();

            return ['locked' => true, 'attempts' => $user['login_attempts']];
        }

        return ['locked' => false, 'attempts' => $user['login_attempts']];
    }

    return ['locked' => false, 'attempts' => 0];
}

/**
 * Reset login attempts for user
 */
function reset_login_attempts($user_id) {
    global $mysqli;

    $stmt = $mysqli->prepare("UPDATE users SET login_attempts = 0, locked_until = NULL WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
}

/**
 * Record failed login attempt
 */
function record_failed_login($username) {
    global $mysqli;

    $stmt = $mysqli->prepare("UPDATE users SET login_attempts = login_attempts + 1 WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $username, $username);
    $stmt->execute();
}

/**
 * Create user session record
 */
function create_user_session($user_id) {
    global $mysqli;

    $session_id = session_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $stmt = $mysqli->prepare("
        INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent, created_at)
        VALUES (?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        ip_address = VALUES(ip_address),
        user_agent = VALUES(user_agent),
        last_activity = NOW()
    ");
    $stmt->bind_param("isss", $user_id, $session_id, $ip_address, $user_agent);
    $stmt->execute();
}

/**
 * Destroy user session record
 */
function destroy_user_session($user_id) {
    global $mysqli;

    $session_id = session_id();

    $stmt = $mysqli->prepare("DELETE FROM user_sessions WHERE user_id = ? AND session_id = ?");
    $stmt->bind_param("is", $user_id, $session_id);
    $stmt->execute();
}

/**
 * Log user activity
 */
function log_activity($action, $table_name = null, $record_id = null, $old_values = null, $new_values = null) {
    global $mysqli;
    
    if (!is_logged_in()) {
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $old_values_json = $old_values ? json_encode($old_values) : null;
    $new_values_json = $new_values ? json_encode($new_values) : null;
    
    $stmt = $mysqli->prepare("
        INSERT INTO activity_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->bind_param("ississss", $user_id, $action, $table_name, $record_id, $old_values_json, $new_values_json, $ip_address, $user_agent);
    $stmt->execute();
}

/**
 * Send notification to user
 */
function send_notification($user_id, $title, $message, $type = 'info', $action_url = null) {
    global $mysqli;
    
    $stmt = $mysqli->prepare("
        INSERT INTO notifications (user_id, title, message, type, action_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->bind_param("issss", $user_id, $title, $message, $type, $action_url);
    return $stmt->execute();
}

/**
 * Format currency
 */
function format_currency($amount, $currency = 'INR') {
    if ($amount === null || $amount === '') {
        $amount = 0;
    }
    return $currency . ' ' . number_format((float)$amount, 2);
}

/**
 * Format date
 */
function format_date($date, $format = 'd-m-Y') {
    if (!$date || $date === '0000-00-00') {
        return '-';
    }
    return date($format, strtotime($date));
}

/**
 * Format datetime
 */
function format_datetime($datetime, $format = 'd-m-Y H:i') {
    if (!$datetime || $datetime === '0000-00-00 00:00:00') {
        return '-';
    }
    return date($format, strtotime($datetime));
}

/**
 * Get file size in human readable format
 */
function format_file_size($bytes) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * Generate unique filename
 */
function generate_unique_filename($original_name) {
    $extension = pathinfo($original_name, PATHINFO_EXTENSION);
    return uniqid() . '_' . time() . '.' . $extension;
}

/**
 * Check if file type is allowed
 */
function is_allowed_file_type($filename) {
    global $mysqli;
    
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    $stmt = $mysqli->prepare("SELECT setting_value FROM company_settings WHERE setting_key = 'allowed_file_types'");
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    $allowed_types = explode(',', $result['setting_value'] ?? '');
    return in_array($extension, $allowed_types);
}

/**
 * Redirect with message
 */
function redirect_with_message($url, $message, $type = 'success') {
    $_SESSION[$type] = $message;
    header('Location: ' . $url);
    exit();
}

/**
 * Get user info by ID
 */
function get_user_info($user_id) {
    global $mysqli;
    
    $stmt = $mysqli->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    return $stmt->get_result()->fetch_assoc();
}

/**
 * Get company setting
 */
function get_setting($key, $default = '') {
    global $mysqli;
    
    $stmt = $mysqli->prepare("SELECT setting_value FROM company_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    return $result ? $result['setting_value'] : $default;
}

/**
 * Update company setting
 */
function update_setting($key, $value, $user_id = null) {
    global $mysqli;
    
    $user_id = $user_id ?? $_SESSION['user_id'] ?? null;
    
    $stmt = $mysqli->prepare("
        INSERT INTO company_settings (setting_key, setting_value, updated_by) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_by = VALUES(updated_by)
    ");
    $stmt->bind_param("ssi", $key, $value, $user_id);
    return $stmt->execute();
}

/**
 * Check and handle remember me token
 */
function check_remember_token() {
    global $mysqli;

    if (isset($_COOKIE['remember_token']) && !is_logged_in()) {
        $token = $_COOKIE['remember_token'];

        $stmt = $mysqli->prepare("SELECT * FROM users WHERE remember_token = ? AND status = 'active'");
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($user = $result->fetch_assoc()) {
            // Auto-login user
            session_regenerate_id(true);

            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['login_time'] = time();

            // Update last login
            $stmt = $mysqli->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $stmt->bind_param("i", $user['id']);
            $stmt->execute();

            // Create user session
            create_user_session($user['id']);

            // Log activity
            log_activity('Auto Login via Remember Token', 'users', $user['id']);

            return true;
        } else {
            // Invalid token, remove cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    }

    return false;
}

/**
 * Clear remember me token
 */
function clear_remember_token($user_id = null) {
    global $mysqli;

    $user_id = $user_id ?? $_SESSION['user_id'] ?? null;

    if ($user_id) {
        $stmt = $mysqli->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
    }

    // Remove cookie
    setcookie('remember_token', '', time() - 3600, '/', '', false, true);
}
?>
