<?php
/**
 * Apex Company Management System
 * View Message
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication
auth_middleware();

$message_id = (int)($_GET['id'] ?? 0);

if (!$message_id) {
    $_SESSION['error'] = 'Invalid message ID.';
    header('Location: index.php');
    exit();
}

// Get message details
$stmt = $mysqli->prepare("
    SELECT m.id, m.subject, m.message, m.priority, m.is_read, m.created_at,
           s.first_name as sender_first_name, s.last_name as sender_last_name, s.email as sender_email,
           r.first_name as recipient_first_name, r.last_name as recipient_last_name, r.email as recipient_email
    FROM messages m
    JOIN users s ON m.sender_id = s.id
    JOIN users r ON m.recipient_id = r.id
    WHERE m.id = ? AND (m.sender_id = ? OR m.recipient_id = ?)
    AND ((m.sender_id = ? AND m.deleted_by_sender = 0) OR (m.recipient_id = ? AND m.deleted_by_recipient = 0))
");
$stmt->bind_param("iiiii", $message_id, $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']);
$stmt->execute();
$message = $stmt->get_result()->fetch_assoc();

if (!$message) {
    $_SESSION['error'] = 'Message not found or you do not have permission to view it.';
    header('Location: index.php');
    exit();
}

// Mark as read if user is the recipient and message is unread
if (!$message['is_read'] && $message['recipient_first_name'] . ' ' . $message['recipient_last_name'] !== $_SESSION['first_name'] . ' ' . $_SESSION['last_name']) {
    $stmt = $mysqli->prepare("UPDATE messages SET is_read = 1, read_at = NOW() WHERE id = ? AND recipient_id = ?");
    $stmt->bind_param("ii", $message_id, $_SESSION['user_id']);
    $stmt->execute();
    $message['is_read'] = 1;
}

// Determine if current user is sender or recipient
$current_user_name = ($_SESSION['first_name'] ?? '') . ' ' . ($_SESSION['last_name'] ?? '');
$sender_name = ($message['sender_first_name'] ?? '') . ' ' . ($message['sender_last_name'] ?? '');
$is_sender = ($sender_name === $current_user_name);

$page_title = 'Message: ' . $message['subject'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-envelope-open"></i>
                    Message Details
                </h1>
                <div class="btn-group">
                    <?php if (!$is_sender): ?>
                    <a href="compose.php?reply=<?= $message_id ?>" class="btn btn-primary">
                        <i class="bi bi-reply"></i>
                        Reply
                    </a>
                    <?php endif; ?>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Messages
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5 class="mb-1"><?= htmlspecialchars($message['subject']) ?></h5>
                            <div class="text-muted small">
                                <strong>From:</strong> <?= htmlspecialchars($message['sender_first_name'] . ' ' . $message['sender_last_name']) ?> 
                                (<?= htmlspecialchars($message['sender_email']) ?>)
                                <br>
                                <strong>To:</strong> <?= htmlspecialchars($message['recipient_first_name'] . ' ' . $message['recipient_last_name']) ?> 
                                (<?= htmlspecialchars($message['recipient_email']) ?>)
                                <br>
                                <strong>Sent:</strong> <?= format_datetime($message['created_at']) ?>
                            </div>
                        </div>
                        <div class="text-end">
                            <?php if ($message['priority'] === 'high'): ?>
                            <span class="badge bg-warning mb-2">High Priority</span>
                            <?php elseif ($message['priority'] === 'urgent'): ?>
                            <span class="badge bg-danger mb-2">Urgent</span>
                            <?php endif; ?>
                            
                            <?php if (!$message['is_read'] && !$is_sender): ?>
                            <br><span class="badge bg-primary">Unread</span>
                            <?php elseif ($message['is_read'] && $is_sender): ?>
                            <br><span class="badge bg-success">Read</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="message-content">
                        <?= nl2br(htmlspecialchars($message['message'])) ?>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex gap-2">
                        <?php if (!$is_sender): ?>
                        <a href="compose.php?reply=<?= $message_id ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-reply"></i>
                            Reply
                        </a>
                        <a href="compose.php?forward=<?= $message_id ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-arrow-right"></i>
                            Forward
                        </a>
                        <?php endif; ?>
                        
                        <form method="POST" action="index.php" class="d-inline">
                            <?= csrf_token_input() ?>
                            <input type="hidden" name="message_id" value="<?= $message_id ?>">
                            <input type="hidden" name="action" value="delete">
                            <button type="submit" class="btn btn-outline-danger btn-sm" 
                                    onclick="return confirm('Are you sure you want to delete this message?')">
                                <i class="bi bi-trash"></i>
                                Delete
                            </button>
                        </form>
                        
                        <?php if (!$is_sender && !$message['is_read']): ?>
                        <form method="POST" action="index.php" class="d-inline">
                            <?= csrf_token_input() ?>
                            <input type="hidden" name="message_id" value="<?= $message_id ?>">
                            <input type="hidden" name="action" value="mark_unread">
                            <button type="submit" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-envelope"></i>
                                Mark as Unread
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">Message Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted small">Priority</label>
                        <div>
                            <?php if ($message['priority'] === 'urgent'): ?>
                            <span class="badge bg-danger">Urgent</span>
                            <?php elseif ($message['priority'] === 'high'): ?>
                            <span class="badge bg-warning">High</span>
                            <?php else: ?>
                            <span class="badge bg-secondary">Normal</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">Status</label>
                        <div>
                            <?php if ($message['is_read']): ?>
                            <span class="badge bg-success">Read</span>
                            <?php else: ?>
                            <span class="badge bg-primary">Unread</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">Sent</label>
                        <div><?= format_datetime($message['created_at']) ?></div>
                    </div>
                    
                    <div class="mb-0">
                        <label class="form-label text-muted small">Message ID</label>
                        <div class="font-monospace small">#<?= $message_id ?></div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (!$is_sender): ?>
                        <a href="compose.php?reply=<?= $message_id ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-reply"></i>
                            Reply to Message
                        </a>
                        <a href="compose.php?forward=<?= $message_id ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-arrow-right"></i>
                            Forward Message
                        </a>
                        <?php endif; ?>
                        
                        <a href="compose.php?to=<?= $is_sender ? $message['recipient_first_name'] . ' ' . $message['recipient_last_name'] : $message['sender_first_name'] . ' ' . $message['sender_last_name'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-plus"></i>
                            New Message to <?= $is_sender ? 'Recipient' : 'Sender' ?>
                        </a>
                        
                        <hr class="my-2">
                        
                        <a href="index.php?filter=inbox" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-inbox"></i>
                            View Inbox
                        </a>
                        <a href="index.php?filter=sent" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-send"></i>
                            View Sent Messages
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-content {
    font-size: 1rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.card-header .text-muted {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>

<?php require_once '../includes/footer.php'; ?>
