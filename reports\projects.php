<?php
/**
 * Apex Company Management System
 * Project Reports
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_reports');

// Get date range parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');

// Get project statistics
$project_stats_query = "
    SELECT 
        COUNT(*) as total_projects,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_projects,
        COUNT(CASE WHEN status = 'on_hold' THEN 1 END) as on_hold_projects,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_projects,
        AVG(progress) as average_progress,
        SUM(budget) as total_budget,
        COUNT(CASE WHEN due_date < CURDATE() AND status != 'completed' THEN 1 END) as overdue_projects
    FROM projects 
    WHERE created_at BETWEEN ? AND ?
";
$stmt = $mysqli->prepare($project_stats_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$project_stats = $stmt->get_result()->fetch_assoc();

// Get project profitability
$profitability_query = "
    SELECT 
        p.id,
        p.project_name,
        p.budget,
        p.status,
        p.progress,
        c.company_name as client_name,
        SUM(i.total_amount) as invoiced_amount,
        (SUM(i.total_amount) - p.budget) as profit_loss
    FROM projects p
    LEFT JOIN clients c ON p.client_id = c.id
    LEFT JOIN invoices i ON p.id = i.project_id AND i.status = 'paid'
    WHERE p.created_at BETWEEN ? AND ?
    AND p.budget IS NOT NULL AND p.budget > 0
    GROUP BY p.id, p.project_name, p.budget, p.status, p.progress, c.company_name
    ORDER BY profit_loss DESC
    LIMIT 20
";
$stmt = $mysqli->prepare($profitability_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$profitability = $stmt->get_result();

// Get overdue projects
$overdue_query = "
    SELECT 
        p.id,
        p.project_name,
        p.due_date,
        p.progress,
        p.status,
        c.company_name as client_name,
        DATEDIFF(CURDATE(), p.due_date) as days_overdue
    FROM projects p
    LEFT JOIN clients c ON p.client_id = c.id
    WHERE p.due_date < CURDATE() 
    AND p.status != 'completed'
    ORDER BY days_overdue DESC
    LIMIT 15
";
$overdue_projects = $mysqli->query($overdue_query);

$page_title = 'Project Reports';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-kanban"></i>
                    Project Reports
                </h1>
                <div class="btn-group">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Reports
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="bi bi-search"></i>
                                Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $project_stats['total_projects'] ?></h4>
                            <p class="mb-0">Total Projects</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-kanban" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75">Avg Progress: <?= number_format($project_stats['average_progress'], 1) ?>%</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $project_stats['completed_projects'] ?></h4>
                            <p class="mb-0">Completed</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $project_stats['active_projects'] ?></h4>
                            <p class="mb-0">In Progress</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-play-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $project_stats['overdue_projects'] ?></h4>
                            <p class="mb-0">Overdue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Project Profitability -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Project Profitability</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Client</th>
                                    <th>Budget</th>
                                    <th>Invoiced</th>
                                    <th>Profit/Loss</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($project = $profitability->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <a href="../projects/view.php?id=<?= $project['id'] ?>" class="text-decoration-none">
                                            <strong><?= htmlspecialchars($project['project_name']) ?></strong>
                                        </a>
                                    </td>
                                    <td><?= htmlspecialchars($project['client_name'] ?? 'No Client') ?></td>
                                    <td><?= format_currency($project['budget']) ?></td>
                                    <td><?= format_currency($project['invoiced_amount']) ?></td>
                                    <td>
                                        <span class="<?= $project['profit_loss'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                            <?= format_currency($project['profit_loss']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge status-<?= $project['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $project['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?= $project['progress'] ?>%">
                                                <?= $project['progress'] ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overdue Projects -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Overdue Projects</h5>
                </div>
                <div class="card-body">
                    <?php if ($overdue_projects->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($overdue = $overdue_projects->fetch_assoc()): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <a href="../projects/view.php?id=<?= $overdue['id'] ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($overdue['project_name']) ?>
                                    </a>
                                </h6>
                                <small class="text-danger"><?= $overdue['days_overdue'] ?> days</small>
                            </div>
                            <p class="mb-1">
                                <?php if ($overdue['client_name']): ?>
                                <small class="text-muted"><?= htmlspecialchars($overdue['client_name']) ?></small><br>
                                <?php endif; ?>
                                <span class="badge status-<?= $overdue['status'] ?>">
                                    <?= ucfirst(str_replace('_', ' ', $overdue['status'])) ?>
                                </span>
                            </p>
                            <div class="progress mb-1" style="height: 15px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: <?= $overdue['progress'] ?>%">
                                    <?= $overdue['progress'] ?>%
                                </div>
                            </div>
                            <small class="text-muted">Due: <?= format_date($overdue['due_date']) ?></small>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        <p class="mt-2 mb-0">No overdue projects</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
