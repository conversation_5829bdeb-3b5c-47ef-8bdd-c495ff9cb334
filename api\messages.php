<?php
/**
 * Apex Company Management System
 * Messages API
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!is_logged_in()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Check permission
if (!has_permission('view_messages')) {
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

$action = $_POST['action'] ?? $_GET['action'] ?? 'get';
$user_id = $_SESSION['user_id'];

switch ($action) {
    case 'unread_count':
        // Get unread message count
        $stmt = $mysqli->prepare("
            SELECT COUNT(*) as count
            FROM messages
            WHERE recipient_id = ? AND is_read = 0 AND deleted_by_recipient = 0
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'count' => (int)$result['count']
        ]);
        break;
        
    case 'get':
        // Get messages for user
        $limit = (int)($_GET['limit'] ?? 20);
        $offset = (int)($_GET['offset'] ?? 0);
        
        $stmt = $mysqli->prepare("
            SELECT m.*,
                   u.first_name, u.last_name, u.profile_image,
                   CASE
                       WHEN m.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN CONCAT(TIMESTAMPDIFF(MINUTE, m.created_at, NOW()), ' min ago')
                       WHEN m.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN CONCAT(TIMESTAMPDIFF(HOUR, m.created_at, NOW()), ' hr ago')
                       ELSE DATE_FORMAT(m.created_at, '%d %b %H:%i')
                   END as time_ago
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE (m.recipient_id = ? AND m.deleted_by_recipient = 0) OR (m.sender_id = ? AND m.deleted_by_sender = 0)
            ORDER BY m.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->bind_param("iiii", $user_id, $user_id, $limit, $offset);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $messages = [];
        while ($row = $result->fetch_assoc()) {
            $messages[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'messages' => $messages
        ]);
        break;
        
    case 'send':
        // Send message
        if (!has_permission('send_messages')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            exit();
        }
        
        $receiver_id = (int)($_POST['receiver_id'] ?? 0);
        $message = sanitize_input($_POST['message'] ?? '');
        $message_type = sanitize_input($_POST['message_type'] ?? 'private');
        
        if (empty($message)) {
            echo json_encode(['success' => false, 'message' => 'Message cannot be empty']);
            exit();
        }
        
        if ($message_type === 'private' && $receiver_id <= 0) {
            echo json_encode(['success' => false, 'message' => 'Receiver is required for private messages']);
            exit();
        }
        
        $stmt = $mysqli->prepare("
            INSERT INTO messages (sender_id, recipient_id, message, message_type)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->bind_param("iiss", $user_id, $receiver_id, $message, $message_type);
        
        if ($stmt->execute()) {
            $message_id = $mysqli->insert_id;
            
            // Send notification to receiver
            if ($receiver_id > 0) {
                $sender_info = get_user_info($user_id);
                $sender_name = $sender_info['first_name'] . ' ' . $sender_info['last_name'];
                
                send_notification(
                    $receiver_id,
                    'New Message',
                    "You have a new message from {$sender_name}",
                    'info',
                    '../messages/'
                );
            }
            
            echo json_encode(['success' => true, 'message_id' => $message_id]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send message']);
        }
        break;
        
    case 'mark_read':
        // Mark message as read
        $message_id = (int)($_POST['message_id'] ?? 0);
        
        if ($message_id > 0) {
            $stmt = $mysqli->prepare("
                UPDATE messages
                SET is_read = 1
                WHERE id = ? AND recipient_id = ?
            ");
            $stmt->bind_param("ii", $message_id, $user_id);
            $stmt->execute();
            
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Invalid message ID']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>
