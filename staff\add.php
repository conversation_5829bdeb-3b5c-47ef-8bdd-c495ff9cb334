<?php
/**
 * Apex Company Management System
 * Add Staff Member
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('create_users');

$error = '';
$success = '';

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $first_name = trim($_POST['first_name'] ?? '');
        $last_name = trim($_POST['last_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $role = $_POST['role'] ?? 'staff';
        $status = $_POST['status'] ?? 'active';
        $salary = !empty($_POST['salary']) ? (float)$_POST['salary'] : null;
        $permissions = $_POST['permissions'] ?? [];
        
        // Validation
        if (empty($username)) {
            $error = 'Username is required.';
        } elseif (empty($email)) {
            $error = 'Email is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } elseif (empty($password)) {
            $error = 'Password is required.';
        } elseif (strlen($password) < PASSWORD_MIN_LENGTH) {
            $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        } elseif ($password !== $confirm_password) {
            $error = 'Passwords do not match.';
        } elseif (empty($first_name)) {
            $error = 'First name is required.';
        } elseif (empty($last_name)) {
            $error = 'Last name is required.';
        } else {
            // Check if username already exists
            $stmt = $mysqli->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $error = 'Username already exists. Please choose a different username.';
            } else {
                // Check if email already exists
                $stmt = $mysqli->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->bind_param("s", $email);
                $stmt->execute();
                if ($stmt->get_result()->num_rows > 0) {
                    $error = 'Email already exists. Please use a different email address.';
                } else {
                    // Hash password
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert user
                    $stmt = $mysqli->prepare("
                        INSERT INTO users (username, email, password_hash, first_name, last_name,
                                         phone, role, status, salary, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ");

                    $stmt->bind_param("ssssssssd",
                        $username, $email, $password_hash, $first_name, $last_name,
                        $phone, $role, $status, $salary
                    );
                    
                    if ($stmt->execute()) {
                        $user_id = $mysqli->insert_id;

                        // Save custom permissions for staff role
                        if ($role === 'staff' && !empty($permissions)) {
                            foreach ($permissions as $permission_id) {
                                $perm_stmt = $mysqli->prepare("
                                    INSERT INTO user_permissions (user_id, permission_id, granted)
                                    VALUES (?, ?, 1)
                                ");
                                $perm_stmt->bind_param("ii", $user_id, $permission_id);
                                $perm_stmt->execute();
                            }
                        }

                        log_activity('Staff Member Created', 'users', $user_id);
                        $_SESSION['success'] = 'Staff member created successfully!';
                        header('Location: view.php?id=' . $user_id);
                        exit();
                    } else {
                        $error = 'Error creating staff member. Please try again.';
                    }
                }
            }
        }
    }
}

$page_title = 'Add Staff Member';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-person-plus"></i>
                    Add Staff Member
                </h1>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    Back to Staff
                </a>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <form method="POST" action="" id="staffForm">
        <?= csrf_token_input() ?>
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= htmlspecialchars($_POST['first_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= htmlspecialchars($_POST['last_name'] ?? '') ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Account Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" required>
                                <div class="form-text">Username must be unique and will be used for login.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?= ($_POST['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= ($_POST['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Minimum <?= PASSWORD_MIN_LENGTH ?> characters required.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                        
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Staff Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <?php if ($_SESSION['role'] === 'super_admin'): ?>
                                    <option value="super_admin" <?= ($_POST['role'] ?? '') === 'super_admin' ? 'selected' : '' ?>>Super Admin</option>
                                    <option value="admin" <?= ($_POST['role'] ?? '') === 'admin' ? 'selected' : '' ?>>Admin</option>
                                    <?php endif; ?>
                                    <option value="staff" <?= ($_POST['role'] ?? 'staff') === 'staff' ? 'selected' : '' ?>>Staff</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="salary" class="form-label">Monthly Salary</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="salary" name="salary"
                                           value="<?= htmlspecialchars($_POST['salary'] ?? '') ?>"
                                           step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="form-text">Leave empty if not applicable</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4" id="permissions-section" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">Permissions</h5>
                        <small class="text-muted">Select specific permissions for this staff member</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php
                            // Get all permissions grouped by module
                            $stmt = $mysqli->prepare("SELECT * FROM permissions ORDER BY module, name");
                            $stmt->execute();
                            $permissions = $stmt->get_result();

                            $grouped_permissions = [];
                            while ($perm = $permissions->fetch_assoc()) {
                                $grouped_permissions[$perm['module']][] = $perm;
                            }

                            foreach ($grouped_permissions as $module => $perms):
                            ?>
                            <div class="col-md-6 mb-4">
                                <h6 class="text-capitalize"><?= ucfirst($module) ?></h6>
                                <?php foreach ($perms as $perm): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           name="permissions[]" value="<?= $perm['id'] ?>"
                                           id="perm_<?= $perm['id'] ?>">
                                    <label class="form-check-label" for="perm_<?= $perm['id'] ?>">
                                        <?= htmlspecialchars($perm['description']) ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 mb-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check"></i>
                        Create Staff Member
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i>
                        Cancel
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Role Permissions</h6>
                    </div>
                    <div class="card-body">
                        <div id="rolePermissions">
                            <p class="text-muted">Select a role to see permissions.</p>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Tips</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="bi bi-info-circle text-primary"></i> Choose a unique username for login.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Use a strong password with mixed characters.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Super Admin has full system access.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Admin can manage most features.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Staff has limited permissions.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const permissionsDiv = document.getElementById('rolePermissions');
    const permissionsSection = document.getElementById('permissions-section');

    // Show/hide custom permissions section for staff role
    if (role === 'staff') {
        permissionsSection.style.display = 'block';
    } else {
        permissionsSection.style.display = 'none';
    }

    let permissions = '';

    switch(role) {
        case 'super_admin':
            permissions = `
                <h6 class="text-danger">Super Administrator</h6>
                <ul class="small">
                    <li>Full system access</li>
                    <li>Manage all users and roles</li>
                    <li>System settings and configuration</li>
                    <li>All module permissions</li>
                    <li>Database and security management</li>
                </ul>
            `;
            break;
        case 'admin':
            permissions = `
                <h6 class="text-warning">Administrator</h6>
                <ul class="small">
                    <li>Manage clients and projects</li>
                    <li>Create and manage invoices</li>
                    <li>View reports and analytics</li>
                    <li>Manage files and documents</li>
                    <li>Limited user management</li>
                </ul>
            `;
            break;
        case 'staff':
            permissions = `
                <h6 class="text-info">Staff Member</h6>
                <ul class="small">
                    <li>View and edit assigned projects</li>
                    <li>Create basic invoices</li>
                    <li>Upload and manage files</li>
                    <li>Send and receive messages</li>
                    <li>View basic reports</li>
                </ul>
            `;
            break;
        default:
            permissions = '<p class="text-muted">Select a role to see permissions.</p>';
    }
    
    permissionsDiv.innerHTML = permissions;
});

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
