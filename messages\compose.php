<?php
/**
 * Apex Company Management System
 * Compose Message
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication
auth_middleware();

$error = '';
$success = '';

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $recipient_id = (int)($_POST['recipient_id'] ?? 0);
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $priority = $_POST['priority'] ?? 'normal';
        
        // Validation
        if (!$recipient_id) {
            $error = 'Please select a recipient.';
        } elseif (empty($subject)) {
            $error = 'Subject is required.';
        } elseif (empty($message)) {
            $error = 'Message content is required.';
        } elseif ($recipient_id == $_SESSION['user_id']) {
            $error = 'You cannot send a message to yourself.';
        } else {
            // Verify recipient exists and is active
            $stmt = $mysqli->prepare("SELECT id FROM users WHERE id = ? AND status = 'active'");
            $stmt->bind_param("i", $recipient_id);
            $stmt->execute();
            if ($stmt->get_result()->num_rows === 0) {
                $error = 'Invalid recipient selected.';
            } else {
                // Insert message
                $stmt = $mysqli->prepare("
                    INSERT INTO messages (sender_id, recipient_id, subject, message, priority, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->bind_param("iisss", $_SESSION['user_id'], $recipient_id, $subject, $message, $priority);
                
                if ($stmt->execute()) {
                    log_activity('Message Sent', 'messages', $stmt->insert_id);
                    $_SESSION['success'] = 'Message sent successfully!';
                    header('Location: index.php');
                    exit();
                } else {
                    $error = 'Error sending message. Please try again.';
                }
            }
        }
    }
}

// Get all active users for recipient selection
$users_query = "
    SELECT id, first_name, last_name, email, role
    FROM users 
    WHERE status = 'active' AND id != ?
    ORDER BY first_name, last_name
";
$stmt = $mysqli->prepare($users_query);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$users = $stmt->get_result();

$page_title = 'Compose Message';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-pencil-square"></i>
                    Compose Message
                </h1>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    Back to Messages
                </a>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">New Message</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" id="composeForm">
                        <?= csrf_token_input() ?>
                        
                        <div class="mb-3">
                            <label for="recipient_id" class="form-label">To <span class="text-danger">*</span></label>
                            <select class="form-select" id="recipient_id" name="recipient_id" required>
                                <option value="">Select recipient...</option>
                                <?php while ($user = $users->fetch_assoc()): ?>
                                <option value="<?= $user['id'] ?>" <?= ($_POST['recipient_id'] ?? '') == $user['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?> 
                                    (<?= htmlspecialchars($user['email']) ?>) 
                                    - <?= ucfirst(str_replace('_', ' ', $user['role'])) ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="subject" name="subject" 
                                       value="<?= htmlspecialchars($_POST['subject'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="normal" <?= ($_POST['priority'] ?? 'normal') === 'normal' ? 'selected' : '' ?>>Normal</option>
                                    <option value="high" <?= ($_POST['priority'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                                    <option value="urgent" <?= ($_POST['priority'] ?? '') === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="10" required><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                            <div class="form-text">You can use basic formatting in your message.</div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send"></i>
                                Send Message
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                                <i class="bi bi-save"></i>
                                Save Draft
                            </button>
                            <a href="index.php" class="btn btn-outline-danger">
                                <i class="bi bi-x"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">Message Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small text-muted">
                        <li><i class="bi bi-info-circle text-primary"></i> Keep your subject line clear and descriptive.</li>
                        <li><i class="bi bi-info-circle text-primary"></i> Use appropriate priority levels.</li>
                        <li><i class="bi bi-info-circle text-primary"></i> Be professional and concise.</li>
                        <li><i class="bi bi-info-circle text-primary"></i> Messages are logged for audit purposes.</li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Priority Levels</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge bg-secondary">Normal</span>
                        <small class="text-muted ms-2">Regular communication</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">High</span>
                        <small class="text-muted ms-2">Important matters requiring attention</small>
                    </div>
                    <div class="mb-0">
                        <span class="badge bg-danger">Urgent</span>
                        <small class="text-muted ms-2">Critical issues needing immediate response</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-save draft functionality
let draftTimer;
let hasChanges = false;

function markChanged() {
    hasChanges = true;
    clearTimeout(draftTimer);
    draftTimer = setTimeout(autoSaveDraft, 30000); // Auto-save after 30 seconds of inactivity
}

function autoSaveDraft() {
    if (hasChanges) {
        saveDraft(true);
    }
}

function saveDraft(isAuto = false) {
    const formData = new FormData(document.getElementById('composeForm'));
    formData.append('action', 'save_draft');
    
    fetch('draft_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            hasChanges = false;
            if (!isAuto) {
                alert('Draft saved successfully!');
            }
        }
    })
    .catch(error => {
        if (!isAuto) {
            alert('Error saving draft. Please try again.');
        }
    });
}

// Add event listeners for auto-save
document.getElementById('subject').addEventListener('input', markChanged);
document.getElementById('message').addEventListener('input', markChanged);
document.getElementById('recipient_id').addEventListener('change', markChanged);
document.getElementById('priority').addEventListener('change', markChanged);

// Warn before leaving if there are unsaved changes
window.addEventListener('beforeunload', function(e) {
    if (hasChanges) {
        e.preventDefault();
        e.returnValue = '';
    }
});

// Character counter for message
document.getElementById('message').addEventListener('input', function() {
    const maxLength = 5000;
    const currentLength = this.value.length;
    
    if (!document.getElementById('charCounter')) {
        const counter = document.createElement('div');
        counter.id = 'charCounter';
        counter.className = 'form-text text-end';
        this.parentNode.appendChild(counter);
    }
    
    const counter = document.getElementById('charCounter');
    counter.textContent = `${currentLength}/${maxLength} characters`;
    
    if (currentLength > maxLength * 0.9) {
        counter.className = 'form-text text-end text-warning';
    } else {
        counter.className = 'form-text text-end text-muted';
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
