-- Migration script to update existing database schema
-- Run this script to fix column name mismatches and add missing tables

USE apex_company_management;

-- Fix projects table - rename end_date to due_date
ALTER TABLE projects 
CHANGE COLUMN end_date due_date DATE;

-- Update project status enum values
ALTER TABLE projects 
MODIFY COLUMN status ENUM('planning', 'in_progress', 'on_hold', 'completed', 'cancelled') NOT NULL DEFAULT 'planning';

-- Fix invoices table - rename invoice_date to issue_date
ALTER TABLE invoices 
CHANGE COLUMN invoice_date issue_date DATE NOT NULL;

-- Fix invoices table - rename payment_terms to terms
ALTER TABLE invoices 
CHANGE COLUMN payment_terms terms TEXT;

-- Make due_date nullable in invoices
ALTER TABLE invoices 
MODIFY COLUMN due_date DATE NULL;

-- Remove discount_rate column from invoices (we only need discount_amount)
ALTER TABLE invoices 
DROP COLUMN IF EXISTS discount_rate;

-- Fix invoice_items table - rename columns
ALTER TABLE invoice_items 
CHANGE COLUMN unit_price rate DECIMAL(10,2) NOT NULL;

ALTER TABLE invoice_items 
CHANGE COLUMN total_price amount DECIMAL(12,2) NOT NULL;

-- Create project_tasks table if it doesn't exist
CREATE TABLE IF NOT EXISTS project_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    assigned_to INT,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
    start_date DATE,
    due_date DATE,
    estimated_hours DECIMAL(8,2),
    actual_hours DECIMAL(8,2) DEFAULT 0,
    progress TINYINT DEFAULT 0,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
);

-- Update project_milestones table structure
ALTER TABLE project_milestones 
CHANGE COLUMN title milestone_name VARCHAR(200) NOT NULL;

ALTER TABLE project_milestones 
MODIFY COLUMN status ENUM('pending', 'completed', 'overdue') NOT NULL DEFAULT 'pending';

ALTER TABLE project_milestones 
CHANGE COLUMN completed_at completion_date DATE;

-- Add missing columns to project_milestones
ALTER TABLE project_milestones 
ADD COLUMN IF NOT EXISTS created_by INT NOT NULL DEFAULT 1;

ALTER TABLE project_milestones 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Add foreign key for created_by in project_milestones
ALTER TABLE project_milestones 
ADD CONSTRAINT fk_milestone_created_by 
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT;

-- Create project_team table if it doesn't exist
CREATE TABLE IF NOT EXISTS project_team (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    user_id INT NOT NULL,
    role VARCHAR(100) DEFAULT 'Team Member',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_user (project_id, user_id)
);

-- Make client_id nullable in projects table (for internal projects)
ALTER TABLE projects 
MODIFY COLUMN client_id INT NULL;

-- Update foreign key constraint for client_id in projects
ALTER TABLE projects 
DROP FOREIGN KEY projects_ibfk_1;

ALTER TABLE projects 
ADD CONSTRAINT fk_project_client 
FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL;

-- Insert some sample data for testing
-- Insert sample project tasks for existing projects (if any)
INSERT IGNORE INTO project_tasks (project_id, task_name, description, status, priority, created_by)
SELECT 
    id as project_id,
    CONCAT('Initial Setup for ', project_name) as task_name,
    'Initial project setup and planning' as description,
    'pending' as status,
    'medium' as priority,
    created_by
FROM projects 
WHERE id NOT IN (SELECT DISTINCT project_id FROM project_tasks);

-- Insert sample milestones for existing projects (if any)
INSERT IGNORE INTO project_milestones (project_id, milestone_name, description, status, created_by)
SELECT 
    id as project_id,
    CONCAT('Phase 1 - ', project_name) as milestone_name,
    'First phase completion' as description,
    'pending' as status,
    created_by
FROM projects 
WHERE id NOT IN (SELECT DISTINCT project_id FROM project_milestones);

-- Update any existing project status values to match new enum
UPDATE projects SET status = 'planning' WHERE status = 'pending';
UPDATE projects SET status = 'in_progress' WHERE status = 'active';

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_tasks_project_id ON project_tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_project_tasks_assigned_to ON project_tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_project_tasks_status ON project_tasks(status);
CREATE INDEX IF NOT EXISTS idx_project_milestones_project_id ON project_milestones(project_id);
CREATE INDEX IF NOT EXISTS idx_project_team_project_id ON project_team(project_id);
CREATE INDEX IF NOT EXISTS idx_project_team_user_id ON project_team(user_id);

-- Update invoice status for overdue invoices
UPDATE invoices
SET status = 'overdue'
WHERE status = 'sent'
AND due_date IS NOT NULL
AND due_date < CURDATE();

-- Create messages table for internal communication
CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('normal', 'high', 'urgent') NOT NULL DEFAULT 'normal',
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    deleted_by_sender BOOLEAN NOT NULL DEFAULT FALSE,
    deleted_by_recipient BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
);

COMMIT;
