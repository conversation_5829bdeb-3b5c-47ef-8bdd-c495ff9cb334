<?php
/**
 * Apex Company Management System
 * Client Reports
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_reports');

// Get date range parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');

// Get client statistics
$client_stats_query = "
    SELECT 
        COUNT(*) as total_clients,
        COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_clients,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_clients,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_clients
    FROM clients
";
$stmt = $mysqli->prepare($client_stats_query);
$stmt->bind_param("s", $start_date);
$stmt->execute();
$client_stats = $stmt->get_result()->fetch_assoc();

// Get top clients by revenue
$top_clients_query = "
    SELECT 
        c.id,
        c.company_name,
        c.email,
        c.phone,
        COUNT(i.id) as total_invoices,
        SUM(i.total_amount) as total_invoiced,
        SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
        COUNT(p.id) as total_projects
    FROM clients c
    LEFT JOIN invoices i ON c.id = i.client_id
    LEFT JOIN projects p ON c.id = p.client_id
    GROUP BY c.id, c.company_name, c.email, c.phone
    ORDER BY total_paid DESC
    LIMIT 20
";
$top_clients = $mysqli->query($top_clients_query);

// Get client activity in date range
$client_activity_query = "
    SELECT 
        c.company_name,
        COUNT(DISTINCT i.id) as invoices_created,
        COUNT(DISTINCT p.id) as projects_created,
        SUM(i.total_amount) as revenue_generated
    FROM clients c
    LEFT JOIN invoices i ON c.id = i.client_id AND i.created_at BETWEEN ? AND ?
    LEFT JOIN projects p ON c.id = p.client_id AND p.created_at BETWEEN ? AND ?
    WHERE c.created_at <= ?
    GROUP BY c.id, c.company_name
    HAVING invoices_created > 0 OR projects_created > 0
    ORDER BY revenue_generated DESC
    LIMIT 15
";
$stmt = $mysqli->prepare($client_activity_query);
$stmt->bind_param("sssss", $start_date, $end_date, $start_date, $end_date, $end_date);
$stmt->execute();
$client_activity = $stmt->get_result();

$page_title = 'Client Reports';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-people"></i>
                    Client Reports
                </h1>
                <div class="btn-group">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Reports
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="bi bi-search"></i>
                                Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $client_stats['total_clients'] ?></h4>
                            <p class="mb-0">Total Clients</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $client_stats['new_clients'] ?></h4>
                            <p class="mb-0">New Clients</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-plus" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75">Since <?= format_date($start_date) ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $client_stats['active_clients'] ?></h4>
                            <p class="mb-0">Active Clients</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $client_stats['inactive_clients'] ?></h4>
                            <p class="mb-0">Inactive Clients</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-pause-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Clients by Revenue -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Top Clients by Revenue</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Contact</th>
                                    <th>Invoices</th>
                                    <th>Projects</th>
                                    <th>Total Invoiced</th>
                                    <th>Total Paid</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($client = $top_clients->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <a href="../clients/view.php?id=<?= $client['id'] ?>" class="text-decoration-none">
                                            <strong><?= htmlspecialchars($client['company_name']) ?></strong>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($client['email']): ?>
                                        <div><?= htmlspecialchars($client['email']) ?></div>
                                        <?php endif; ?>
                                        <?php if ($client['phone']): ?>
                                        <small class="text-muted"><?= htmlspecialchars($client['phone']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $client['total_invoices'] ?></td>
                                    <td><?= $client['total_projects'] ?></td>
                                    <td><?= format_currency($client['total_invoiced']) ?></td>
                                    <td>
                                        <strong class="text-success"><?= format_currency($client['total_paid']) ?></strong>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Activity in Date Range -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Client Activity</h5>
                    <small class="text-muted"><?= format_date($start_date) ?> to <?= format_date($end_date) ?></small>
                </div>
                <div class="card-body">
                    <?php if ($client_activity->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($activity = $client_activity->fetch_assoc()): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?= htmlspecialchars($activity['company_name']) ?></h6>
                                <small class="text-success"><?= format_currency($activity['revenue_generated']) ?></small>
                            </div>
                            <p class="mb-1">
                                <small class="text-muted">
                                    <?= $activity['invoices_created'] ?> invoices, 
                                    <?= $activity['projects_created'] ?> projects
                                </small>
                            </p>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                        <p class="mt-2 mb-0">No activity in selected period</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
