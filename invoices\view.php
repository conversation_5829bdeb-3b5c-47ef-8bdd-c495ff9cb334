<?php
/**
 * Apex Company Management System
 * View Invoice
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_invoices');

$invoice_id = (int)($_GET['id'] ?? 0);

if (!$invoice_id) {
    $_SESSION['error'] = 'Invalid invoice ID.';
    header('Location: index.php');
    exit();
}

// Get invoice details with client and project info
$stmt = $mysqli->prepare("
    SELECT i.*,
           c.company_name as client_name,
           c.email as client_email,
           c.phone as client_phone,
           c.address as client_address,
           p.project_name
    FROM invoices i
    LEFT JOIN clients c ON i.client_id = c.id
    LEFT JOIN projects p ON i.project_id = p.id
    WHERE i.id = ?
");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$invoice = $stmt->get_result()->fetch_assoc();

if (!$invoice) {
    $_SESSION['error'] = 'Invoice not found.';
    header('Location: index.php');
    exit();
}

// Get invoice items
$stmt = $mysqli->prepare("
    SELECT * FROM invoice_items 
    WHERE invoice_id = ? 
    ORDER BY id
");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$items = $stmt->get_result();

$page_title = 'Invoice #' . $invoice['invoice_number'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-receipt"></i>
                    Invoice #<?= htmlspecialchars($invoice['invoice_number']) ?>
                </h1>
                <div class="btn-group">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Invoices
                    </a>
                    <?php if (has_permission('edit_invoices')): ?>
                    <a href="edit.php?id=<?= $invoice['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                    <?php endif; ?>
                    <a href="pdf.php?id=<?= $invoice['id'] ?>" class="btn btn-info" target="_blank">
                        <i class="bi bi-file-pdf"></i>
                        Download PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4 class="text-primary">INVOICE</h4>
                            <p class="mb-1"><strong>Invoice #:</strong> <?= htmlspecialchars($invoice['invoice_number']) ?></p>
                            <p class="mb-1"><strong>Issue Date:</strong> <?= format_date($invoice['issue_date']) ?></p>
                            <?php if ($invoice['due_date']): ?>
                            <p class="mb-1"><strong>Due Date:</strong> <?= format_date($invoice['due_date']) ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h5>Your Company Name</h5>
                            <p class="mb-0">Your Address</p>
                            <p class="mb-0">City, State ZIP</p>
                            <p class="mb-0">Phone: Your Phone</p>
                            <p class="mb-0">Email: <EMAIL></p>
                        </div>
                    </div>

                    <!-- Client Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Bill To:</h6>
                            <?php if ($invoice['client_name']): ?>
                            <p class="mb-1"><strong><?= htmlspecialchars($invoice['client_name']) ?></strong></p>
                            <?php if ($invoice['client_address']): ?>
                            <p class="mb-1"><?= nl2br(htmlspecialchars($invoice['client_address'])) ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['client_phone']): ?>
                            <p class="mb-1">Phone: <?= htmlspecialchars($invoice['client_phone']) ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['client_email']): ?>
                            <p class="mb-1">Email: <?= htmlspecialchars($invoice['client_email']) ?></p>
                            <?php endif; ?>
                            <?php else: ?>
                            <p class="text-muted">No client assigned</p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <?php if ($invoice['project_name']): ?>
                            <h6>Project:</h6>
                            <p><?= htmlspecialchars($invoice['project_name']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Description</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-end">Rate</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $subtotal = 0;
                                while ($item = $items->fetch_assoc()): 
                                    $amount = $item['quantity'] * $item['rate'];
                                    $subtotal += $amount;
                                ?>
                                <tr>
                                    <td><?= htmlspecialchars($item['description']) ?></td>
                                    <td class="text-center"><?= $item['quantity'] ?></td>
                                    <td class="text-end"><?= format_currency($item['rate']) ?></td>
                                    <td class="text-end"><?= format_currency($amount) ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Invoice Totals -->
                    <div class="row">
                        <div class="col-md-6">
                            <?php if ($invoice['notes']): ?>
                            <h6>Notes:</h6>
                            <p><?= nl2br(htmlspecialchars($invoice['notes'])) ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td class="text-end"><strong>Subtotal:</strong></td>
                                    <td class="text-end"><?= format_currency($subtotal) ?></td>
                                </tr>
                                <?php if ($invoice['tax_rate'] > 0): ?>
                                <tr>
                                    <td class="text-end">Tax (<?= $invoice['tax_rate'] ?>%):</td>
                                    <td class="text-end"><?= format_currency(($subtotal * $invoice['tax_rate']) / 100) ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($invoice['discount_amount'] > 0): ?>
                                <tr>
                                    <td class="text-end">Discount:</td>
                                    <td class="text-end">-<?= format_currency($invoice['discount_amount']) ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr class="table-primary">
                                    <td class="text-end"><strong>Total:</strong></td>
                                    <td class="text-end"><strong><?= format_currency($invoice['total_amount']) ?></strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Invoice Status</h6>
                </div>
                <div class="card-body">
                    <p><strong>Status:</strong> 
                        <span class="badge status-<?= $invoice['status'] ?>">
                            <?= ucfirst($invoice['status']) ?>
                        </span>
                    </p>
                    <p><strong>Created:</strong> <?= format_date($invoice['created_at']) ?></p>
                    <?php if ($invoice['updated_at']): ?>
                    <p><strong>Updated:</strong> <?= format_date($invoice['updated_at']) ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($invoice['terms']): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Payment Terms</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(htmlspecialchars($invoice['terms'])) ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
