<?php
/**
 * Apex Company Management System
 * Financial Reports
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_reports');

// Get date range parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month
$report_type = $_GET['report_type'] ?? 'summary';

// Validate dates
if (!strtotime($start_date) || !strtotime($end_date)) {
    $start_date = date('Y-m-01');
    $end_date = date('Y-m-t');
}

// Get financial summary data
$summary_query = "
    SELECT 
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as pending_invoices,
        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN status = 'sent' THEN total_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'overdue' THEN total_amount ELSE 0 END) as overdue_amount,
        SUM(total_amount) as total_invoiced
    FROM invoices 
    WHERE issue_date BETWEEN ? AND ?
";
$stmt = $mysqli->prepare($summary_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$summary = $stmt->get_result()->fetch_assoc();

// Get monthly revenue trend (last 12 months)
$monthly_query = "
    SELECT 
        DATE_FORMAT(issue_date, '%Y-%m') as month,
        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as revenue,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
        SUM(total_amount) as total_invoiced
    FROM invoices 
    WHERE issue_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(issue_date, '%Y-%m')
    ORDER BY month DESC
    LIMIT 12
";
$monthly_result = $mysqli->query($monthly_query);

// Get client revenue breakdown
$client_query = "
    SELECT 
        c.company_name,
        COUNT(i.id) as invoice_count,
        SUM(i.total_amount) as total_invoiced,
        SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
        SUM(CASE WHEN i.status IN ('sent', 'overdue') THEN i.total_amount ELSE 0 END) as outstanding
    FROM clients c
    LEFT JOIN invoices i ON c.id = i.client_id AND i.issue_date BETWEEN ? AND ?
    GROUP BY c.id, c.company_name
    HAVING total_invoiced > 0
    ORDER BY total_paid DESC
    LIMIT 10
";
$stmt = $mysqli->prepare($client_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$client_revenue = $stmt->get_result();

// Get project profitability
$project_query = "
    SELECT 
        p.project_name,
        p.budget,
        SUM(i.total_amount) as invoiced_amount,
        (SUM(i.total_amount) - p.budget) as profit_loss,
        CASE 
            WHEN p.budget > 0 THEN ((SUM(i.total_amount) - p.budget) / p.budget * 100)
            ELSE 0 
        END as profit_margin
    FROM projects p
    LEFT JOIN invoices i ON p.id = i.project_id AND i.status = 'paid'
    WHERE p.created_at BETWEEN ? AND ?
    AND p.budget IS NOT NULL AND p.budget > 0
    GROUP BY p.id, p.project_name, p.budget
    ORDER BY profit_margin DESC
    LIMIT 10
";
$stmt = $mysqli->prepare($project_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$project_profitability = $stmt->get_result();

$page_title = 'Financial Reports';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-graph-up"></i>
                    Financial Reports
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        Print Report
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToCSV()">
                        <i class="bi bi-download"></i>
                        Export CSV
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-3">
                    <label for="report_type" class="form-label">Report Type</label>
                    <select class="form-select" id="report_type" name="report_type">
                        <option value="summary" <?= $report_type === 'summary' ? 'selected' : '' ?>>Summary</option>
                        <option value="detailed" <?= $report_type === 'detailed' ? 'selected' : '' ?>>Detailed</option>
                        <option value="trends" <?= $report_type === 'trends' ? 'selected' : '' ?>>Trends</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        Generate Report
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($summary['total_revenue']) ?></h4>
                            <p class="mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-currency-dollar" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75"><?= $summary['paid_invoices'] ?> paid invoices</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($summary['pending_amount']) ?></h4>
                            <p class="mb-0">Pending Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75"><?= $summary['pending_invoices'] ?> pending invoices</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($summary['overdue_amount']) ?></h4>
                            <p class="mb-0">Overdue Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75"><?= $summary['overdue_invoices'] ?> overdue invoices</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($summary['total_invoiced']) ?></h4>
                            <p class="mb-0">Total Invoiced</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-receipt" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <small class="opacity-75">All invoice statuses</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Monthly Revenue Trend -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Monthly Revenue Trend (Last 12 Months)</h5>
                </div>
                <div class="card-body">
                    <?php if ($monthly_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Revenue</th>
                                    <th>Paid Invoices</th>
                                    <th>Total Invoiced</th>
                                    <th>Collection Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($month = $monthly_result->fetch_assoc()): ?>
                                <?php $collection_rate = $month['total_invoiced'] > 0 ? ($month['revenue'] / $month['total_invoiced'] * 100) : 0; ?>
                                <tr>
                                    <td><?= date('M Y', strtotime($month['month'] . '-01')) ?></td>
                                    <td><?= format_currency($month['revenue']) ?></td>
                                    <td><?= $month['paid_count'] ?></td>
                                    <td><?= format_currency($month['total_invoiced']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $collection_rate >= 80 ? 'success' : ($collection_rate >= 60 ? 'warning' : 'danger') ?>">
                                            <?= number_format($collection_rate, 1) ?>%
                                        </span>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                        <p class="mt-2">No revenue data available for the selected period.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Top Clients by Revenue -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Top Clients by Revenue</h5>
                </div>
                <div class="card-body">
                    <?php if ($client_revenue->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($client = $client_revenue->fetch_assoc()): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= htmlspecialchars($client['company_name']) ?></h6>
                                    <p class="mb-1 text-muted small">
                                        <?= $client['invoice_count'] ?> invoices
                                    </p>
                                </div>
                                <div class="text-end">
                                    <strong><?= format_currency($client['total_paid']) ?></strong>
                                    <?php if ($client['outstanding'] > 0): ?>
                                    <br><small class="text-warning">Outstanding: <?= format_currency($client['outstanding']) ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-people" style="font-size: 3rem;"></i>
                        <p class="mt-2">No client revenue data available.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Profitability -->
    <?php if ($project_profitability->num_rows > 0): ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Project Profitability Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Budget</th>
                                    <th>Invoiced</th>
                                    <th>Profit/Loss</th>
                                    <th>Margin</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($project = $project_profitability->fetch_assoc()): ?>
                                <tr>
                                    <td><?= htmlspecialchars($project['project_name']) ?></td>
                                    <td><?= format_currency($project['budget']) ?></td>
                                    <td><?= format_currency($project['invoiced_amount']) ?></td>
                                    <td class="<?= $project['profit_loss'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <?= format_currency($project['profit_loss']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $project['profit_margin'] >= 20 ? 'success' : ($project['profit_margin'] >= 0 ? 'warning' : 'danger') ?>">
                                            <?= number_format($project['profit_margin'], 1) ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($project['profit_margin'] >= 20): ?>
                                        <span class="badge bg-success">Excellent</span>
                                        <?php elseif ($project['profit_margin'] >= 10): ?>
                                        <span class="badge bg-primary">Good</span>
                                        <?php elseif ($project['profit_margin'] >= 0): ?>
                                        <span class="badge bg-warning">Break-even</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">Loss</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function exportToCSV() {
    // Create CSV content
    let csv = 'Financial Report - ' + '<?= $start_date ?>' + ' to ' + '<?= $end_date ?>' + '\n\n';
    
    // Summary data
    csv += 'Summary\n';
    csv += 'Total Revenue,<?= $summary['total_revenue'] ?>\n';
    csv += 'Pending Amount,<?= $summary['pending_amount'] ?>\n';
    csv += 'Overdue Amount,<?= $summary['overdue_amount'] ?>\n';
    csv += 'Total Invoiced,<?= $summary['total_invoiced'] ?>\n\n';
    
    // Download CSV
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'financial_report_<?= $start_date ?>_to_<?= $end_date ?>.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}
</script>

<?php require_once '../includes/footer.php'; ?>
