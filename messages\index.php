<?php
/**
 * Apex Company Management System
 * Messages - Inbox
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication
auth_middleware();

// Get current user ID
$user_id = $_SESSION['user_id'];

// Handle message actions
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $_SESSION['error'] = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        $message_id = (int)($_POST['message_id'] ?? 0);

        if ($action === 'mark_read' && $message_id) {
            $stmt = $mysqli->prepare("UPDATE messages SET is_read = 1 WHERE id = ? AND recipient_id = ?");
            $stmt->bind_param("ii", $message_id, $user_id);
            $stmt->execute();
        } elseif ($action === 'mark_unread' && $message_id) {
            $stmt = $mysqli->prepare("UPDATE messages SET is_read = 0 WHERE id = ? AND recipient_id = ?");
            $stmt->bind_param("ii", $message_id, $user_id);
            $stmt->execute();
        } elseif ($action === 'delete' && $message_id) {
            $stmt = $mysqli->prepare("UPDATE messages SET deleted_by_recipient = 1 WHERE id = ? AND recipient_id = ?");
            $stmt->bind_param("ii", $message_id, $user_id);
            $stmt->execute();
        }
    }

    header('Location: index.php');
    exit();
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'inbox';
$search = sanitize_input($_GET['search'] ?? '');

// Build query based on filter
$where_conditions = ["m.recipient_id = ?", "m.deleted_by_recipient = 0"];
$params = [$user_id];
$param_types = 'i';

if ($filter === 'unread') {
    $where_conditions[] = "m.is_read = 0";
} elseif ($filter === 'sent') {
    $where_conditions = ["m.sender_id = ?", "m.deleted_by_sender = 0"];
    $params = [$user_id];
}

if (!empty($search)) {
    $where_conditions[] = "(m.subject LIKE ? OR m.message LIKE ? OR CONCAT(u.first_name, ' ', u.last_name) LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $param_types .= 'sss';
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get messages
$query = "
    SELECT m.id, m.subject, m.message, m.is_read, m.created_at, m.priority,
           u.first_name, u.last_name, u.email,
           " . ($filter === 'sent' ? 'r.first_name as recipient_first_name, r.last_name as recipient_last_name' : 's.first_name as sender_first_name, s.last_name as sender_last_name') . "
    FROM messages m
    " . ($filter === 'sent' ?
        'JOIN users u ON m.sender_id = u.id
         JOIN users r ON m.recipient_id = r.id' :
        'JOIN users u ON m.sender_id = u.id
         LEFT JOIN users s ON m.sender_id = s.id') . "
    $where_clause
    ORDER BY m.created_at DESC
    LIMIT 50
";

$stmt = $mysqli->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$messages = $stmt->get_result();

// Get message counts
$counts_query = "
    SELECT
        COUNT(CASE WHEN recipient_id = ? AND deleted_by_recipient = 0 THEN 1 END) as inbox_count,
        COUNT(CASE WHEN recipient_id = ? AND is_read = 0 AND deleted_by_recipient = 0 THEN 1 END) as unread_count,
        COUNT(CASE WHEN sender_id = ? AND deleted_by_sender = 0 THEN 1 END) as sent_count
    FROM messages
";
$stmt = $mysqli->prepare($counts_query);
$stmt->bind_param("iii", $user_id, $user_id, $user_id);
$stmt->execute();
$counts = $stmt->get_result()->fetch_assoc();

$page_title = 'Messages';
require_once '../includes/header.php';
?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-envelope"></i>
                    Messages
                </h1>
                <a href="compose.php" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    Compose Message
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="index.php?filter=inbox" class="list-group-item list-group-item-action <?= $filter === 'inbox' ? 'active' : '' ?>">
                            <i class="bi bi-inbox"></i>
                            Inbox
                            <span class="badge bg-primary float-end"><?= $counts['inbox_count'] ?></span>
                        </a>
                        <a href="index.php?filter=unread" class="list-group-item list-group-item-action <?= $filter === 'unread' ? 'active' : '' ?>">
                            <i class="bi bi-envelope"></i>
                            Unread
                            <span class="badge bg-danger float-end"><?= $counts['unread_count'] ?></span>
                        </a>
                        <a href="index.php?filter=sent" class="list-group-item list-group-item-action <?= $filter === 'sent' ? 'active' : '' ?>">
                            <i class="bi bi-send"></i>
                            Sent
                            <span class="badge bg-secondary float-end"><?= $counts['sent_count'] ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages List -->
        <div class="col-md-9">
            <!-- Search and Filters -->
            <div class="card mb-3">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="filter" value="<?= htmlspecialchars($filter) ?>">
                        <div class="col-md-10">
                            <input type="text" class="form-control" name="search"
                                   value="<?= htmlspecialchars($search) ?>"
                                   placeholder="Search messages...">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Messages -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <?php
                        switch($filter) {
                            case 'unread': echo 'Unread Messages'; break;
                            case 'sent': echo 'Sent Messages'; break;
                            default: echo 'Inbox'; break;
                        }
                        ?>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if ($messages->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($message = $messages->fetch_assoc()): ?>
                        <div class="list-group-item list-group-item-action <?= !$message['is_read'] && $filter !== 'sent' ? 'bg-light' : '' ?>">
                            <div class="d-flex w-100 justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <?php if ($filter === 'sent'): ?>
                                        <strong class="me-2">To: <?= htmlspecialchars($message['recipient_first_name'] . ' ' . $message['recipient_last_name']) ?></strong>
                                        <?php else: ?>
                                        <strong class="me-2">From: <?= htmlspecialchars($message['first_name'] . ' ' . $message['last_name']) ?></strong>
                                        <?php endif; ?>

                                        <?php if ($message['priority'] === 'high'): ?>
                                        <span class="badge bg-danger me-2">High Priority</span>
                                        <?php elseif ($message['priority'] === 'urgent'): ?>
                                        <span class="badge bg-warning me-2">Urgent</span>
                                        <?php endif; ?>

                                        <?php if (!$message['is_read'] && $filter !== 'sent'): ?>
                                        <span class="badge bg-primary">New</span>
                                        <?php endif; ?>
                                    </div>
                                    <h6 class="mb-1">
                                        <a href="view.php?id=<?= $message['id'] ?>" class="text-decoration-none">
                                            <?= htmlspecialchars($message['subject']) ?>
                                        </a>
                                    </h6>
                                    <p class="mb-1 text-muted">
                                        <?= htmlspecialchars(substr(strip_tags($message['message']), 0, 100)) ?>
                                        <?= strlen(strip_tags($message['message'])) > 100 ? '...' : '' ?>
                                    </p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted d-block"><?= format_datetime($message['created_at']) ?></small>
                                    <div class="btn-group btn-group-sm mt-1">
                                        <a href="view.php?id=<?= $message['id'] ?>" class="btn btn-outline-primary" title="View">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <?php if ($filter !== 'sent'): ?>
                                        <form method="POST" class="d-inline">
                                            <?= csrf_token_input() ?>
                                            <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                            <input type="hidden" name="action" value="<?= $message['is_read'] ? 'mark_unread' : 'mark_read' ?>">
                                            <button type="submit" class="btn btn-outline-secondary" title="<?= $message['is_read'] ? 'Mark as Unread' : 'Mark as Read' ?>">
                                                <i class="bi bi-<?= $message['is_read'] ? 'envelope' : 'envelope-open' ?>"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        <form method="POST" class="d-inline">
                                            <?= csrf_token_input() ?>
                                            <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                            <input type="hidden" name="action" value="delete">
                                            <button type="submit" class="btn btn-outline-danger" title="Delete"
                                                    onclick="return confirm('Are you sure you want to delete this message?')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-envelope" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">No Messages Found</h4>
                        <p>
                            <?php if ($filter === 'sent'): ?>
                                You haven't sent any messages yet.
                            <?php elseif ($filter === 'unread'): ?>
                                You have no unread messages.
                            <?php else: ?>
                                Your inbox is empty.
                            <?php endif; ?>
                        </p>
                        <a href="compose.php" class="btn btn-primary mt-3">
                            <i class="bi bi-plus"></i>
                            Send Your First Message
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
