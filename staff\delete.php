<?php
/**
 * Apex Company Management System
 * Delete Staff Member
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('delete_users');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

if (!verify_csrf_token($_POST['csrf_token'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit();
}

$staff_id = (int)($_POST['id'] ?? 0);

if (!$staff_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid staff ID']);
    exit();
}

// Get staff member details
$stmt = $mysqli->prepare("SELECT id, username, first_name, last_name, role FROM users WHERE id = ?");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$staff = $stmt->get_result()->fetch_assoc();

if (!$staff) {
    echo json_encode(['success' => false, 'message' => 'Staff member not found']);
    exit();
}

// Prevent deletion of current user
if ($staff_id == $_SESSION['user_id']) {
    echo json_encode(['success' => false, 'message' => 'You cannot delete your own account']);
    exit();
}

// Prevent deletion of super admin by non-super admin
if ($staff['role'] === 'super_admin' && $_SESSION['role'] !== 'super_admin') {
    echo json_encode(['success' => false, 'message' => 'You cannot delete a super administrator']);
    exit();
}

// Check for dependencies
$dependencies = [];

// Check for projects created by this user
$stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM projects WHERE created_by = ?");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$project_count = $stmt->get_result()->fetch_assoc()['count'];
if ($project_count > 0) {
    $dependencies[] = "$project_count project(s) created by this user";
}

// Check for invoices created by this user
$stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM invoices WHERE created_by = ?");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$invoice_count = $stmt->get_result()->fetch_assoc()['count'];
if ($invoice_count > 0) {
    $dependencies[] = "$invoice_count invoice(s) created by this user";
}

// Check for project tasks assigned to this user
$stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM project_tasks WHERE assigned_to = ?");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$task_count = $stmt->get_result()->fetch_assoc()['count'];
if ($task_count > 0) {
    $dependencies[] = "$task_count project task(s) assigned to this user";
}

// Check for project team memberships
$stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM project_team WHERE user_id = ?");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$team_count = $stmt->get_result()->fetch_assoc()['count'];
if ($team_count > 0) {
    $dependencies[] = "$team_count project team membership(s)";
}

if (!empty($dependencies)) {
    $message = 'Cannot delete staff member. The following dependencies exist: ' . implode(', ', $dependencies);
    echo json_encode(['success' => false, 'message' => $message]);
    exit();
}

// Begin transaction
$mysqli->begin_transaction();

try {
    // Remove from project teams
    $stmt = $mysqli->prepare("DELETE FROM project_team WHERE user_id = ?");
    $stmt->bind_param("i", $staff_id);
    $stmt->execute();
    
    // Update project tasks to unassign this user
    $stmt = $mysqli->prepare("UPDATE project_tasks SET assigned_to = NULL WHERE assigned_to = ?");
    $stmt->bind_param("i", $staff_id);
    $stmt->execute();
    
    // Delete activity logs for this user
    $stmt = $mysqli->prepare("DELETE FROM activity_logs WHERE user_id = ?");
    $stmt->bind_param("i", $staff_id);
    $stmt->execute();
    
    // Delete the user
    $stmt = $mysqli->prepare("DELETE FROM users WHERE id = ?");
    $stmt->bind_param("i", $staff_id);
    $stmt->execute();
    
    if ($stmt->affected_rows === 0) {
        throw new Exception('Failed to delete staff member');
    }
    
    // Log the deletion
    log_activity('Staff Member Deleted', 'users', $staff_id, 
                 "Deleted staff member: {$staff['first_name']} {$staff['last_name']} ({$staff['username']})");
    
    $mysqli->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Staff member deleted successfully'
    ]);
    
} catch (Exception $e) {
    $mysqli->rollback();
    echo json_encode([
        'success' => false, 
        'message' => 'Error deleting staff member: ' . $e->getMessage()
    ]);
}
?>
