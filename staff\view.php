<?php
/**
 * Apex Company Management System
 * View Staff Member
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_users');

$staff_id = (int)($_GET['id'] ?? 0);

if (!$staff_id) {
    $_SESSION['error'] = 'Invalid staff member ID.';
    header('Location: index.php');
    exit();
}

// Get staff member details
$stmt = $mysqli->prepare("
    SELECT id, username, email, first_name, last_name, phone, role, status, 
           last_login, created_at, updated_at
    FROM users 
    WHERE id = ?
");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$staff = $stmt->get_result()->fetch_assoc();

if (!$staff) {
    $_SESSION['error'] = 'Staff member not found.';
    header('Location: index.php');
    exit();
}

// Get staff statistics
$stats_query = "
    SELECT 
        (SELECT COUNT(*) FROM projects WHERE created_by = ?) as projects_created,
        (SELECT COUNT(*) FROM invoices WHERE created_by = ?) as invoices_created,
        (SELECT COUNT(*) FROM project_tasks WHERE assigned_to = ?) as tasks_assigned,
        (SELECT COUNT(*) FROM project_team WHERE user_id = ?) as projects_involved
";
$stmt = $mysqli->prepare($stats_query);
$stmt->bind_param("iiii", $staff_id, $staff_id, $staff_id, $staff_id);
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();

// Get recent projects
$projects_query = "
    SELECT p.id, p.project_name, p.status, p.due_date, c.company_name
    FROM projects p
    LEFT JOIN clients c ON p.client_id = c.id
    WHERE p.created_by = ? OR p.id IN (SELECT project_id FROM project_team WHERE user_id = ?)
    ORDER BY p.created_at DESC
    LIMIT 5
";
$stmt = $mysqli->prepare($projects_query);
$stmt->bind_param("ii", $staff_id, $staff_id);
$stmt->execute();
$recent_projects = $stmt->get_result();

// Get recent tasks
$tasks_query = "
    SELECT pt.id, pt.task_name, pt.status, pt.priority, pt.due_date, p.project_name
    FROM project_tasks pt
    JOIN projects p ON pt.project_id = p.id
    WHERE pt.assigned_to = ?
    ORDER BY pt.created_at DESC
    LIMIT 5
";
$stmt = $mysqli->prepare($tasks_query);
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$recent_tasks = $stmt->get_result();

// Get recent activity
$activity_query = "
    SELECT action, table_name, record_id, details, created_at
    FROM activity_logs
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT 10
";
$stmt = $mysqli->prepare($activity_query);
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$recent_activity = $stmt->get_result();

$page_title = $staff['first_name'] . ' ' . $staff['last_name'] . ' - Staff Details';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-person"></i>
                    Staff Details
                </h1>
                <div class="btn-group">
                    <?php if (has_permission('edit_users') && ($staff_id != $_SESSION['user_id'] || $_SESSION['role'] === 'super_admin')): ?>
                    <a href="edit.php?id=<?= $staff_id ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                    <?php endif; ?>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Staff
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Staff Information -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <?= strtoupper(substr($staff['first_name'], 0, 1) . substr($staff['last_name'], 0, 1)) ?>
                    </div>
                    <h4 class="mb-1"><?= htmlspecialchars($staff['first_name'] . ' ' . $staff['last_name']) ?></h4>
                    <p class="text-muted mb-2">@<?= htmlspecialchars($staff['username']) ?></p>
                    <span class="badge bg-<?= $staff['role'] === 'super_admin' ? 'danger' : ($staff['role'] === 'admin' ? 'warning' : 'info') ?> mb-3">
                        <?= ucfirst(str_replace('_', ' ', $staff['role'])) ?>
                    </span>
                    <br>
                    <span class="badge status-<?= $staff['status'] ?>">
                        <?= ucfirst($staff['status']) ?>
                    </span>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted small">Email</label>
                        <div><?= htmlspecialchars($staff['email']) ?></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted small">Phone</label>
                        <div><?= htmlspecialchars($staff['phone'] ?: 'Not provided') ?></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted small">Last Login</label>
                        <div>
                            <?php if ($staff['last_login']): ?>
                                <?= format_datetime($staff['last_login']) ?>
                            <?php else: ?>
                                <span class="text-muted">Never</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="mb-0">
                        <label class="form-label text-muted small">Member Since</label>
                        <div><?= format_date($staff['created_at']) ?></div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary mb-0"><?= $stats['projects_created'] ?></h4>
                            <small class="text-muted">Projects Created</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success mb-0"><?= $stats['invoices_created'] ?></h4>
                            <small class="text-muted">Invoices Created</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning mb-0"><?= $stats['tasks_assigned'] ?></h4>
                            <small class="text-muted">Tasks Assigned</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info mb-0"><?= $stats['projects_involved'] ?></h4>
                            <small class="text-muted">Projects Involved</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity and Projects -->
        <div class="col-lg-8">
            <!-- Recent Projects -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Recent Projects</h6>
                    <a href="../projects/index.php?staff=<?= $staff_id ?>" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if ($recent_projects->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Client</th>
                                    <th>Status</th>
                                    <th>Due Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($project = $recent_projects->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <a href="../projects/view.php?id=<?= $project['id'] ?>" class="text-decoration-none">
                                            <?= htmlspecialchars($project['project_name']) ?>
                                        </a>
                                    </td>
                                    <td><?= htmlspecialchars($project['company_name'] ?: 'Internal') ?></td>
                                    <td>
                                        <span class="badge status-<?= $project['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $project['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($project['due_date']): ?>
                                            <?= format_date($project['due_date']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-folder" style="font-size: 2rem;"></i>
                        <p class="mb-0 mt-2">No projects found</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Tasks -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Recent Tasks</h6>
                </div>
                <div class="card-body">
                    <?php if ($recent_tasks->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Task</th>
                                    <th>Project</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Due Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($task = $recent_tasks->fetch_assoc()): ?>
                                <tr>
                                    <td><?= htmlspecialchars($task['task_name']) ?></td>
                                    <td><?= htmlspecialchars($task['project_name']) ?></td>
                                    <td>
                                        <span class="badge priority-<?= $task['priority'] ?>">
                                            <?= ucfirst($task['priority']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge status-<?= $task['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($task['due_date']): ?>
                                            <?= format_date($task['due_date']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-list-task" style="font-size: 2rem;"></i>
                        <p class="mb-0 mt-2">No tasks assigned</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Recent Activity</h6>
                </div>
                <div class="card-body">
                    <?php if ($recent_activity->num_rows > 0): ?>
                    <div class="timeline">
                        <?php while ($activity = $recent_activity->fetch_assoc()): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1"><?= htmlspecialchars($activity['action']) ?></h6>
                                <p class="mb-1 text-muted small">
                                    <?= htmlspecialchars($activity['details'] ?: 'No details available') ?>
                                </p>
                                <small class="text-muted"><?= format_datetime($activity['created_at']) ?></small>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-clock-history" style="font-size: 2rem;"></i>
                        <p class="mb-0 mt-2">No recent activity</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 2rem;
    font-weight: bold;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.priority-low { background-color: #28a745; }
.priority-medium { background-color: #ffc107; }
.priority-high { background-color: #fd7e14; }
.priority-urgent { background-color: #dc3545; }
</style>

<?php require_once '../includes/footer.php'; ?>
