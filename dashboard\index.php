<?php
/**
 * Apex Company Management System
 * Dashboard
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication
auth_middleware();

// Check permission
permission_middleware('view_dashboard');

// Get dashboard statistics
$stats = [];

// Total Clients
if (has_permission('view_clients')) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM clients WHERE status = 'active'");
    $stmt->execute();
    $stats['total_clients'] = $stmt->get_result()->fetch_assoc()['count'];
}

// Active Projects
if (has_permission('view_projects')) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM projects WHERE status = 'active'");
    $stmt->execute();
    $stats['active_projects'] = $stmt->get_result()->fetch_assoc()['count'];
}

// Total Sales (This Month)
if (has_permission('view_invoices')) {
    $stmt = $mysqli->prepare("
        SELECT COALESCE(SUM(total_amount), 0) as total
        FROM invoices
        WHERE status = 'paid' AND MONTH(issue_date) = MONTH(CURRENT_DATE()) AND YEAR(issue_date) = YEAR(CURRENT_DATE())
    ");
    $stmt->execute();
    $stats['monthly_sales'] = $stmt->get_result()->fetch_assoc()['total'];
}

// Staff Count
if (has_permission('view_users')) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $stmt->execute();
    $stats['staff_count'] = $stmt->get_result()->fetch_assoc()['count'];
}

// Recent Activities
$recent_activities = [];
if (has_permission('view_users')) {
    $stmt = $mysqli->prepare("
        SELECT al.*, u.first_name, u.last_name 
        FROM activity_logs al 
        JOIN users u ON al.user_id = u.id 
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $recent_activities[] = $row;
    }
}

// Recent Projects
$recent_projects = [];
if (has_permission('view_projects')) {
    $stmt = $mysqli->prepare("
        SELECT p.*, c.company_name, u.first_name, u.last_name 
        FROM projects p 
        LEFT JOIN clients c ON p.client_id = c.id 
        LEFT JOIN users u ON p.assigned_to = u.id 
        ORDER BY p.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $recent_projects[] = $row;
    }
}

// Pending Invoices
$pending_invoices = [];
if (has_permission('view_invoices')) {
    $stmt = $mysqli->prepare("
        SELECT i.*, c.company_name 
        FROM invoices i 
        JOIN clients c ON i.client_id = c.id 
        WHERE i.status IN ('sent', 'overdue') 
        ORDER BY i.due_date ASC 
        LIMIT 5
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $pending_invoices[] = $row;
    }
}

$page_title = 'Dashboard';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-speedometer2"></i>
                    Dashboard
                </h1>
                <div class="text-muted">
                    Welcome back, <?php echo $_SESSION['user_name']; ?>!
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php if (isset($stats['total_clients'])): ?>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo number_format($stats['total_clients']); ?></div>
                        <div class="stats-label">Total Clients</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($stats['active_projects'])): ?>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo number_format($stats['active_projects']); ?></div>
                        <div class="stats-label">Active Projects</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-kanban"></i>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($stats['monthly_sales'])): ?>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo format_currency($stats['monthly_sales']); ?></div>
                        <div class="stats-label">This Month Sales</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($stats['staff_count'])): ?>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo number_format($stats['staff_count']); ?></div>
                        <div class="stats-label">Staff Members</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-person-badge"></i>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="row">
        <!-- Recent Projects -->
        <?php if (has_permission('view_projects')): ?>
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-kanban"></i>
                        Recent Projects
                    </h5>
                    <a href="../projects/" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_projects)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-kanban" style="font-size: 3rem;"></i>
                            <p class="mt-2">No projects found</p>
                            <?php if (has_permission('create_projects')): ?>
                                <a href="../projects/add.php" class="btn btn-primary">Create First Project</a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_projects as $project): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold"><?php echo htmlspecialchars($project['project_name']); ?></div>
                                        <small class="text-muted">
                                            Client: <?php echo htmlspecialchars($project['company_name'] ?? 'N/A'); ?>
                                            <?php if ($project['first_name']): ?>
                                                | Assigned to: <?php echo htmlspecialchars($project['first_name'] . ' ' . $project['last_name']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <span class="badge status-<?php echo $project['status']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $project['status'])); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Pending Invoices -->
        <?php if (has_permission('view_invoices')): ?>
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-receipt"></i>
                        Pending Invoices
                    </h5>
                    <a href="../invoices/" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_invoices)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-receipt" style="font-size: 3rem;"></i>
                            <p class="mt-2">No pending invoices</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($pending_invoices as $invoice): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($invoice['company_name']); ?>
                                            | Due: <?php echo format_date($invoice['due_date']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold"><?php echo format_currency($invoice['total_amount']); ?></div>
                                        <span class="badge status-<?php echo $invoice['status']; ?>">
                                            <?php echo ucfirst($invoice['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Recent Activity -->
    <?php if (has_permission('view_users') && !empty($recent_activities)): ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history"></i>
                        Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?></strong>
                                    <?php echo htmlspecialchars($activity['action']); ?>
                                    <?php if ($activity['table_name']): ?>
                                        in <?php echo htmlspecialchars($activity['table_name']); ?>
                                    <?php endif; ?>
                                </div>
                                <small class="text-muted">
                                    <?php echo format_datetime($activity['created_at']); ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
