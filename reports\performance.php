<?php
/**
 * Apex Company Management System
 * Performance Reports
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_reports');

// Get date range parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');

// Get staff performance data
$staff_performance_query = "
    SELECT 
        u.id,
        u.name,
        u.email,
        COUNT(DISTINCT p.id) as projects_assigned,
        COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END) as projects_completed,
        AVG(p.progress) as average_progress,
        SUM(p.budget) as total_budget_managed
    FROM users u
    LEFT JOIN projects p ON u.id = p.assigned_to 
        AND p.created_at BETWEEN ? AND ?
    WHERE u.role IN ('admin', 'manager', 'staff')
    GROUP BY u.id, u.name, u.email
    ORDER BY projects_completed DESC, average_progress DESC
    LIMIT 20
";
$stmt = $mysqli->prepare($staff_performance_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$staff_performance = $stmt->get_result();

// Get activity statistics
$activity_stats_query = "
    SELECT 
        COUNT(CASE WHEN action = 'Project Created' THEN 1 END) as projects_created,
        COUNT(CASE WHEN action = 'Invoice Created' THEN 1 END) as invoices_created,
        COUNT(CASE WHEN action = 'Client Created' THEN 1 END) as clients_created,
        COUNT(CASE WHEN action = 'Project Updated' THEN 1 END) as projects_updated,
        COUNT(*) as total_activities
    FROM activity_log 
    WHERE created_at BETWEEN ? AND ?
";
$stmt = $mysqli->prepare($activity_stats_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$activity_stats = $stmt->get_result()->fetch_assoc();

// Get recent activities
$recent_activities_query = "
    SELECT 
        al.*,
        u.name as user_name
    FROM activity_log al
    LEFT JOIN users u ON al.user_id = u.id
    WHERE al.created_at BETWEEN ? AND ?
    ORDER BY al.created_at DESC
    LIMIT 20
";
$stmt = $mysqli->prepare($recent_activities_query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$recent_activities = $stmt->get_result();

$page_title = 'Performance Reports';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-graph-up"></i>
                    Performance Reports
                </h1>
                <div class="btn-group">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Reports
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="bi bi-search"></i>
                                Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $activity_stats['total_activities'] ?></h4>
                            <p class="mb-0">Total Activities</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-activity" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $activity_stats['projects_created'] ?></h4>
                            <p class="mb-0">Projects Created</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-kanban" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $activity_stats['invoices_created'] ?></h4>
                            <p class="mb-0">Invoices Created</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-receipt" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $activity_stats['clients_created'] ?></h4>
                            <p class="mb-0">Clients Added</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Staff Performance -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Staff Performance</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Staff Member</th>
                                    <th>Projects Assigned</th>
                                    <th>Projects Completed</th>
                                    <th>Completion Rate</th>
                                    <th>Avg Progress</th>
                                    <th>Budget Managed</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($staff = $staff_performance->fetch_assoc()): ?>
                                <?php 
                                $completion_rate = $staff['projects_assigned'] > 0 ? 
                                    ($staff['projects_completed'] / $staff['projects_assigned'] * 100) : 0;
                                ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($staff['name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($staff['email']) ?></small>
                                        </div>
                                    </td>
                                    <td><?= $staff['projects_assigned'] ?></td>
                                    <td><?= $staff['projects_completed'] ?></td>
                                    <td>
                                        <span class="badge <?= $completion_rate >= 80 ? 'bg-success' : ($completion_rate >= 60 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= number_format($completion_rate, 1) ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?= $staff['average_progress'] ?>%">
                                                <?= number_format($staff['average_progress'], 1) ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= format_currency($staff['total_budget_managed']) ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Activities</h5>
                    <small class="text-muted"><?= format_date($start_date) ?> to <?= format_date($end_date) ?></small>
                </div>
                <div class="card-body">
                    <?php if ($recent_activities->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($activity = $recent_activities->fetch_assoc()): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?= htmlspecialchars($activity['action']) ?></h6>
                                <small><?= date('M j, g:i A', strtotime($activity['created_at'])) ?></small>
                            </div>
                            <p class="mb-1">
                                <small class="text-muted">
                                    by <?= htmlspecialchars($activity['user_name'] ?? 'Unknown') ?>
                                </small>
                            </p>
                            <?php if ($activity['details']): ?>
                            <small class="text-muted"><?= htmlspecialchars($activity['details']) ?></small>
                            <?php endif; ?>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-activity" style="font-size: 2rem;"></i>
                        <p class="mt-2 mb-0">No activities in selected period</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
